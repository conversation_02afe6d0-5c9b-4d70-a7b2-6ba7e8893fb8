#!/bin/bash
set -e

# 显示当前目录
echo "Current directory: $(pwd)"

# 安装依赖
echo "Installing dependencies..."
npm install --force

# 构建项目
echo "Building project..."

# 检查是否提供了自定义API域名
if [ -n "$1" ]; then
  echo "Using custom API domain: $1"
  export NUXT_PUBLIC_API_BASE="$1"
else
  echo "Using default API domain: https://api.dinq.io"
  export NUXT_PUBLIC_API_BASE="https://api.dinq.io"
fi

# 检查是否提供了自定义站点域名
if [ -n "$2" ]; then
  echo "Using custom site URL: $2"
  export NUXT_PUBLIC_SITE_URL="$2"
else
  echo "Using default site URL: https://dinq.io"
  export NUXT_PUBLIC_SITE_URL="https://dinq.io"
fi

# 执行生产环境构建
npm run build:prod

echo "Build completed successfully!"
echo "Static files are available in .output/public directory"
echo "You can deploy these files to any static file hosting service"
