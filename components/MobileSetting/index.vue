<template>
  <Teleport to="body">
    <!-- 遮罩层 -->
    <transition name="fade">
      <div v-if="showMobileMenu" class="drawer-overlay" @click="closeMobileMenu"></div>
    </transition>

    <!-- 抽屉主体 -->
    <transition name="drawer">
      <div v-if="showMobileMenu" class="drawer" :class="{ 'dark-mobile-menu': isDarkBackground }">
        <div class="fx-cer gap-4 pt-15 px-5" @click.stop="closeMobileMenu">
          <img src="~/assets/image/Left.svg" alt="" class="w-5 h-5 btn-icon-light" />
          <img src="~/assets/image/Left2.svg" alt="" class="w-5 h-5 btn-icon-dark" />
          <span>Setting</span>
        </div>
        <div class="drawer-content">
          <div class="mobile-user-section">
            <div v-if="currentUser" class="mobile-user-info">
              <!-- 用户信息 -->
              <div class="mobile-avatar">
                <img
                  v-if="
                    userProfile?.profile_picture ||
                    firebaseUserProfile?.photo_url ||
                    currentUser.photoURL
                  "
                  :src="
                    userProfile?.profile_picture ||
                    firebaseUserProfile?.photo_url ||
                    currentUser.photoURL
                  "
                  alt="User avatar"
                  class="mobile-avatar-img"
                />
                <div v-else class="mobile-avatar-placeholder">
                  <div class="i-carbon:user text-2xl text-gray-400"></div>
                </div>
              </div>
              <div class="mobile-user-details">
                <div class="mobile-user-name">
                  {{
                    userProfile?.display_name ||
                    firebaseUserProfile?.display_name ||
                    currentUser.displayName ||
                    'User'
                  }}
                </div>
                <div class="mobile-user-email">{{ currentUser.email }}</div>
                <img src="~/assets/svg/verified.svg" alt="" />
              </div>
            </div>

            <div v-else class="mobile-login-section">
              <button class="mobile-login-btn" @click="closeMobileMenu">Login / Sign Up</button>
            </div>
          </div>

          <nav class="mobile-nav">
            <div class="mobile-nav-item">
              <img src="~/assets/image/id-card 3.svg" alt="" class="w-5 h-5 btn-icon-light" />
              <img src="~/assets/image/id-card-dark.svg" alt="" class="w-5 h-5 btn-icon-dark" />
              <span>DINQ Card</span>
            </div>
            <div class="mobile-nav-item">
              <img src="~/assets/image/verified 1.svg" alt="" class="w-5 h-5 btn-icon-light" />
              <img src="~/assets/image/verified 1.svg" alt="" class="w-5 h-5 btn-icon-dark" />
              <span>Verifcation</span>
            </div>
            <div class="mobile-nav-item">
              <img src="~/assets/image/Agreement1.svg" alt="" class="w-5 h-5 btn-icon-light" />
              <img src="~/assets/image/Agreement2.svg" alt="" class="w-5 h-5 btn-icon-dark" />
              <span>Terms & Conditions</span>
            </div>
            <div class="mobile-nav-item">
              <img src="~/assets/image/data-security1.svg" alt="" class="w-5 h-5 btn-icon-light" />
              <img src="~/assets/image/data-security2.svg" alt="" class="w-5 h-5 btn-icon-dark" />
              <span>Privacy policy</span>
            </div>
            <div class="mobile-nav-item" @click.stop="onSettings">
              <img src="~/assets/image/Config1.svg" alt="" class="w-5 h-5 btn-icon-light" />
              <img src="~/assets/image/Config2.svg" alt="" class="w-5 h-5 btn-icon-dark" />
              <span>Settings</span>
            </div>
          </nav>

          <button v-if="currentUser" class="mobile-logout-btn" @click="handleMobileLogout">
            <img src="~/assets/image/power-off.svg" alt="" class="w-5 h-5" />
            Sign Out
          </button>
        </div>
      </div>
    </transition>
    <SettingsModal
      v-if="showSettingsModal"
      :user="{
        photoURL:
          userProfile?.profile_picture || firebaseUserProfile?.photo_url || currentUser?.photoURL,
        name:
          userProfile?.display_name ||
          firebaseUserProfile?.display_name ||
          currentUser?.displayName,
        username: currentUser?.username || currentUser?.email || '',
        verified: currentUser?.verified || true,
      }"
      :tab="activeTab"
      @close="closeSettingsModal"
      @signout="handleMobileLogout"
    />
  </Teleport>
</template>

<script setup lang="ts">
  import { ref, watch, onUnmounted, defineProps, defineEmits } from 'vue'
  import { getCurrentUser, getCurrentFirebaseUser, updateUserInfo } from '@/api/user'

  const props = defineProps({
    show: {
      type: Boolean,
      default: false,
    },
  })
  const emit = defineEmits(['update:show'])

  const showMobileMenu = ref(props.show)
  const showModal = ref(false)
  const isDarkBackground = ref(false)

  const { $emitter } = useNuxtApp()
  const router = useRouter()
  const { currentUser, logout } = useFirebaseAuth()

  const userProfile = ref<any>(null)
  const firebaseUserProfile = ref<any>(null)
  // 设置模态框状态
  const showSettingsModal = ref(false)
  const activeTab = ref('settings')

  function closeSettingsModal() {
    showSettingsModal.value = false
    // 通知导航栏恢复z-index
    $emitter.emit('settings-modal', false)
  }

    function onSettings() {
    activeTab.value = 'settings'
    showSettingsModal.value = true
    // 通知导航栏降低z-index
    $emitter.emit('settings-modal', true)
  }

  watch(
    () => props.show,
    val => {
      showMobileMenu.value = val
      // document.body.style.overflow = val ? 'hidden' : ''
    },
    { immediate: true }
  )

  const closeMobileMenu = () => {
    showMobileMenu.value = false
    emit('update:show', false)
    document.body.style.overflow = ''
  }

  const fetchUserProfile = async () => {
    if (!currentUser.value?.uid) return
    const [userRes, firebaseRes] = await Promise.all([
      getCurrentUser({ Userid: currentUser.value.uid }),
      getCurrentFirebaseUser({ Userid: currentUser.value.uid }),
    ])
    if (userRes.success) userProfile.value = userRes.user
    if (firebaseRes.success) firebaseUserProfile.value = firebaseRes.firebase_user
  }

  watch(
    () => currentUser.value?.uid,
    uid => {
      if (uid) fetchUserProfile()
      else {
        userProfile.value = null
        firebaseUserProfile.value = null
      }
    },
    { immediate: true }
  )

  const handleMobileLogout = () => {
    closeMobileMenu()
    logout()
    router.push('/')
  }

  onUnmounted(() => {
    document.body.style.overflow = ''
  })
</script>

<style scoped>
  /* 抽屉遮罩 */
  .drawer-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 45;
  }

  /* 抽屉本体 */
  .drawer {
    position: fixed;
    inset: 0; /* 全屏 */
    width: 100vw;
    height: 100vh;
    background: white;
    z-index: 50;
    transform: translateX(0%);
  }

  /* 内容区域 */
  .drawer-content {
    padding: 16px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .dark .drawer {
    background: #1a1a1a;
  }

  /* 动画 */
  .drawer-enter-active,
  .drawer-leave-active {
    transition: transform 0.3s ease;
  }
  .drawer-enter-from,
  .drawer-leave-to {
    transform: translateX(100%);
  }
  .drawer-enter-to,
  .drawer-leave-from {
    transform: translateX(0%);
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
  .fade-enter-to,
  .fade-leave-from {
    opacity: 1;
  }

  .mobile-user-section {
    padding: 0 24px 0 24px;
    margin-bottom: 8px;
  }

  .mobile-user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    border-radius: 16px;
    background-color: #f7f8fa;
    border: 1px solid #efefef;
  }

  .dark .mobile-user-info {
    background-color: #242425;
    border-color: #323232;
  }

  .mobile-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
  }

  .mobile-avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .mobile-avatar-placeholder {
    width: 100%;
    height: 100%;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }

  .dark .mobile-avatar-placeholder {
    background: #374151;
  }

  .mobile-user-details {
    display: flex;
    gap: 8px;
    align-items: center;
    flex: 1;
    min-width: 0;
  }

  .mobile-user-name {
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .dark .mobile-user-name {
    color: #f9fafb;
  }

  .mobile-user-email {
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    color: #6b7280;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .dark .mobile-user-email {
    color: #9ca3af;
  }

  .mobile-login-section {
    text-align: center;
  }

  .mobile-login-btn {
    width: 100%;
    padding: 12px 20px;
    background: #000000;
    color: #ffffff;
    border: none;
    border-radius: 8px;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .mobile-login-btn:hover {
    background: #333333;
  }

  .dark .mobile-login-btn {
    background: #ffffff;
    color: #000000;
  }

  .dark .mobile-login-btn:hover {
    background: #f3f4f6;
  }

  .mobile-nav {
    margin: 0 24px;
    display: flex;
    flex-direction: column;
    border: 1px solid #eaeaea;
    border-radius: 8px;
  }

  .mobile-nav-item {
    padding: 10px 16px;
    height: 44px;
    border-bottom: 1px solid #eaeaea;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .dark .mobile-nav {
    background: #242425;
    border-color: #323232;
  }

  .dark .mobile-nav-item {
    background: #242425;
    border-color: #323232;
  }

  .mobile-logout-btn {
    margin: 20px 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px;
    background: black;
    color: #fff;
    border-radius: 8px;
    font-family: 'Alexandria', sans-serif;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 12px;
  }

  .dark .mobile-logout-btn { 
    background: #292929;
  }
  .login-btn {
    @apply bg-white rounded-full transition-all;
    width: 120px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Poppins;
    font-weight: 500;
    font-size: 16px;
    line-height: 160%;
    letter-spacing: 0%;
    text-align: center;
    color: #000000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .login-btn:hover {
    background-color: #f8f9fa;
  }

  .login-btn:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .dark .login-btn {
    @apply bg-[#2A2A2A];
    color: #e3e3e3;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .dark .login-btn:hover {
    background-color: #353535;
  }

  .dark .login-btn:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }
</style>
