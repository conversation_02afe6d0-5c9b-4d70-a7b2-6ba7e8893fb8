<template>
  <div
    class="people-card rounded-2xl p-5 flex flex-col items-center text-center w-[280px] h-[234px] mx-auto overflow-hidden"
  >
    <div class="relative flex items-center justify-center">
      <!-- 加载状态骨架屏 -->
      <div
        v-show="isLoading && !imageError"
        class="avatar-skeleton rounded-full"
        style="width: 56px; height: 56px"
      ></div>

      <!-- 主要头像图片 -->
      <NuxtImg
        v-show="!imageError && !isLoading"
        :src="people.image"
        preload
        :alt="people.name"
        style="width: 56px; height: 56px"
        class="rounded-full object-cover border-white text-center shadow avatar-fade-in"
        @error="handleImageError"
        @load="handleImageLoad"
      />

      <!-- 默认头像 -->
      <img
        v-show="imageError"
        src="/image/avator.png"
        style="width: 56px; height: 56px"
        :alt="people.name"
        class="rounded-full object-cover avatar-fade-in"
      />
    </div>
    <div
      class="text-xl font-bold text-gray-900 truncate max-w-full"
      style="font-family: 'Poppins', 'Inter', sans-serif; font-size: 18px; margin-top: 8px; font-weight: 700"
    >
      {{ people.name }}
    </div>
    <div
      class="position-text text-base mb-4 break-words line-clamp-3 max-h-20 overflow-hidden"
      style="font-size: 12px"
    >
      {{ people.position }}, {{ people.institution }}
    </div>

    <div class="flex gap-4 w-full mt-auto justify-center">
      <button
        class="compare-btn rounded-xl text-sm bg-white hover:bg-gray-50 transition-all"
        style="width: 115px; height: 38px; font-size: 14px"
        @click.stop="handleCompare"
      >
        Compare
      </button>
      <button
        class="analyze-btn rounded-xl text-sm transition-all"
        style="width: 115px; height: 38px; font-size: 14px"
        @click.stop="handleAnalyze"
      >
        Analyze
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  interface People {
    name: string
    position: string
    institution: string
    image: string
    google_scholar?: string
  }
  
  const props = defineProps<{
    people: People
    index?: number
  }>()
  
  const emit = defineEmits<{
    search: [data: { type: 'scholar' | 'name', identifier: string }]
    compare: [identifier: string]
  }>()
  const imageError = ref(false)
  const isLoading = ref(true)

  function handleImageError() {
    imageError.value = true
    isLoading.value = false
  }

  function handleImageLoad() {
    isLoading.value = false
  }
  function handleAnalyze() {
    if (props.people.google_scholar) {
      // 有 Google Scholar ID，可以直接跳转到 Scholar 页面
      console.log('PeopleCard handleAnalyze: Using Scholar ID', {
        name: props.people.name,
        google_scholar: props.people.google_scholar,
        type: 'scholar'
      })
      emit('search', { type: 'scholar', identifier: props.people.google_scholar })
    } else {
      // 只有 name，需要通过 Report 过境页分析
      console.log('PeopleCard handleAnalyze: Using name fallback', {
        name: props.people.name,
        type: 'name'
      })
      emit('search', { type: 'name', identifier: props.people.name })
    }
  }
  function handleCompare() {
    // Compare 功能始终使用过境逻辑，确保一致性
    const identifier = props.people.google_scholar || props.people.name
    console.log('PeopleCard handleCompare: Using transit logic', {
      name: props.people.name,
      google_scholar: props.people.google_scholar,
      using_identifier: identifier
    })
    emit('compare', identifier)
  }
</script>

<style scoped>
  .people-card {
    background: #FFFFFF99;
    backdrop-filter: blur(34px);
    position: relative;
  }

  .people-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 16px;
    padding: 1px;
    background: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0.4) 100%);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    pointer-events: none;
  }

  .dark .people-card {
    background: #141415;
    border: 1px solid #27282D;
    box-shadow: 0px 3px 8px 0px #0000001A;
    backdrop-filter: none;
  }

  .dark .people-card::before {
    display: none;
  }

  .position-text {
    line-height: 150%;
    color: #495160;
  }

  .dark .position-text {
    color: #7A7A7A;
  }

  .compare-btn {
    border: 1px solid #000000;
    color: #000000;
    font-weight: 400;
  }

  .compare-btn:hover {
    background: #F8F9FA;
    border-color: #333333;
  }

  .dark .compare-btn {
    background: transparent;
    border: 1px solid #3E3E3E;
    color: #FFFFFF;
  }

  .dark .compare-btn:hover {
    background: #2A2A2A;
    border-color: #5A5A5A;
  }

  .analyze-btn {
    background: #000000;
    color: #FFFFFF;
    font-weight: 500;
    border: none;
  }

  .analyze-btn:hover {
    background: #333333;
  }

  .dark .analyze-btn {
    background: #FAF9F5;
    color: #000000;
    border: none;
  }

  .dark .analyze-btn:hover {
    background: #E8E7E3;
  }

  /* 头像加载效果 */
  .avatar-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
  }

  .dark .avatar-skeleton {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
  }

  @keyframes skeleton-loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }

  .avatar-fade-in {
    animation: fadeIn 0.8s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  /* 多行省略（line-clamp）兼容性处理 */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
