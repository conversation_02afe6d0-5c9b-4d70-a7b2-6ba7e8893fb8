<template>
  <div class="loader">
    <div v-for="n in 12" :key="n" :style="getStyle(n)"></div>
  </div>
</template>

<script setup>
const colors = [
  '#000000',
  '#222222',
  '#444444',
  '#666666',
  '#888888',
  '#aaaaaa',
  '#cccccc',
  '#eeeeee',
  '#f2e3df',
  '#e2c6ba',
  '#d2a896',
  '#c28b72',
]

const getStyle = (i) => {
  const rotation = (i - 1) * 30
  const delay = -(1.2 - (i - 1) * 0.1).toFixed(1)
  const background = colors[i - 1]
  return {
    transform: `rotate(${rotation}deg)`,
    animationDelay: `${delay}s`,
    background,
  }
}
</script>

<style scoped>
.loader {
  position: relative;
  width: 40px;
  height: 40px;
  margin: 10px auto;
}

.loader div {
  position: absolute;
  top: 0;
  left: 18px;
  width: 4px;
  height: 10px;
  border-radius: 2px;
  transform-origin: 2px 20px;
  animation: fade 1.2s linear infinite;
}

@keyframes fade {
  0% { opacity: 1; }
  100% { opacity: 0; }
}
</style>
