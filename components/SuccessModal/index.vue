<template>
  <transition name="fade">
    <div
      v-if="visible"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 px-4"
      @click.self="handleBackdropClick"
    >
      <transition name="scale">
        <div class="bg-white dark:bg-[#141415] rounded-5 shadow-xl p-5 md:p-5 p-4 w-136 max-w-full rel" v-show="visible">
          <!-- 标题 -->
          <div class="font-bold text-7 md:text-7 text-6 text-black dark:text-white text-center mt-5">
            {{ title }}
          </div>

          <!-- 消息内容 -->
          <div class="mt-4 mb-5 text-sm leading-relaxed text-gray-600 dark:text-gray-400 text-center">
            {{ message }}
          </div>

          <!-- 按钮 -->
          <button
            class="bg-black dark:bg-white rounded-2.5 uppercase transition-colors hover:bg-black/80 dark:hover:bg-gray-100 py-4 w-full text-white dark:text-black text-base md:text-base text-sm font-medium"
            @click="close"
          >
            {{ buttonText }}
          </button>

          <!-- 关闭按钮 -->
          <div v-if="showCloseButton" class="abs top-5 right-5">
            <div
              class="wh-6 text-#333 i-material-symbols:close cursor-pointer opacity-32 dark:text-white"
              @click="close"
            ></div>
          </div>
        </div>
      </transition>
    </div>
  </transition>
</template>

<script setup lang="ts">
  import { computed, withDefaults } from 'vue'

  export type ModalType = 'success' | 'info' | 'warning' | 'error'

  interface Props {
    visible: boolean
    type?: ModalType
    title?: string
    message?: string
    buttonText?: string
    showCloseButton?: boolean
    allowBackdropClose?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    type: 'success',
    title: '',
    message: '',
    buttonText: 'OK',
    showCloseButton: true,
    allowBackdropClose: true
  })

  const emit = defineEmits<{
    (e: 'update:visible', val: boolean): void
    (e: 'close'): void
  }>()

  // 计算属性：根据类型设置默认标题
  const title = computed(() => {
    if (props.title) return props.title

    switch (props.type) {
      case 'success':
        return 'Success'
      case 'info':
        return 'Information'
      case 'warning':
        return 'Warning'
      case 'error':
        return 'Error'
      default:
        return 'Success'
    }
  })

  const close = () => {
    emit('update:visible', false)
    emit('close')
  }

  const handleBackdropClick = () => {
    if (props.allowBackdropClose) {
      close()
    }
  }
</script>

<style scoped>
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }
  
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
  
  .scale-enter-active,
  .scale-leave-active {
    transition: transform 0.3s ease, opacity 0.3s ease;
  }
  
  .scale-enter-from,
  .scale-leave-to {
    transform: scale(0.9);
    opacity: 0;
  }

  /* 移动端优化 */
  @media (max-width: 640px) {
    .rounded-5 {
      border-radius: 1rem;
    }
  }
</style> 