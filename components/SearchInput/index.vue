<template>
  <div class="search-input-wrapper">
    <div class="fx-cer flex-1 gap-4">
      <img src="~/assets/image/search.png" alt="" class="wh-6" />
      <div class="search-input-container">
        <input
          v-model="searchValue"
          class="flex-1 outline-none bg-transparent font-bold text-black search-input"
          @keyup.enter="handleEnterSearch"
          @focus="handleFocus"
          @blur="handleBlur"
          @input="handleInput"
          @compositionstart="handleCompositionStart"
          @compositionupdate="handleCompositionUpdate"
          @compositionend="handleCompositionEnd"
        />
        <!-- Dynamic placeholder display (only shown when carousel is enabled) -->
        <div v-if="enableCarousel && !searchValue.trim() && !isComposing" class="animated-placeholder">
          <Transition name="placeholder-fade" mode="out-in">
            <span :key="currentPlaceholderIndex" class="placeholder-text">{{ currentPlaceholder }}</span>
          </Transition>
        </div>
        <!-- Static placeholder (when carousel is not enabled) -->
        <div v-else-if="!enableCarousel && !searchValue.trim() && !isComposing" class="static-placeholder">
          <span class="placeholder-text">{{ staticPlaceholder }}</span>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  placeholder?: string
  type?: string
  enableCarousel?: boolean
}>()

const emit = defineEmits<{
  enterSearch: [query: string]
}>()

const searchValue = ref('')
const isUserFocused = ref(false)
const isComposing = ref(false) // Track IME input state
const intervalId = ref<NodeJS.Timeout | null>(null)
const isMobile = ref(false) // Track mobile device

// Check if device is mobile
onMounted(() => {
  const checkMobile = () => {
    isMobile.value = window.innerWidth <= 768
  }

  checkMobile()
  window.addEventListener('resize', checkMobile)

  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile)
  })
})

// Carousel related state
const currentPlaceholderIndex = ref(0)

// Scholar mode carousel text (mixed names, URL examples and hint text)
const scholarCarouselTexts = [
  'Enter Google Scholar URL or Just Name', // Hint text - start
  'https://scholar.google.com/citations?user=WLN3QrAAAAAJ',
  'Lukasz Kaiser',
  'Paste any Google Scholar link here', // Hint text - middle
  'https://scholar.google.com/citations?user=dOad5HoAAAAJ',
  'Llion Jones'
]

// Mobile Scholar mode carousel text (simplified for mobile)
const mobileScholarCarouselTexts = [
  'Scholar Name', // Hint text - start
  'Lukasz Kaiser',
  'Google Scholar link', // Hint text - middle
  'Llion Jones'
]

// GitHub mode carousel text
const githubCarouselTexts = [
  'Enter Github Username or URL', // Hint text - start
  'mrkaye97',
  'sooperset',
  'Paste any GitHub profile link here', // Hint text - middle
  'hiyouga',
  'lgrammel'
]

// Mobile GitHub mode carousel text (simplified for mobile)
const mobileGithubCarouselTexts = [
  'Github Username', // Hint text - start
  'mrkaye97',
  'GitHub profile link', // Hint text - middle
  'hiyouga'
]

// Determine if it's hint text
const isHelpText = (text: string) => {
  return text.includes('Enter ') || text.includes('Paste any ') ||
         text === 'Scholar Name' || text === 'Google Scholar link' ||
         text === 'Github Username' || text === 'GitHub profile link'
}

// Get carousel text based on type and device
const carouselTexts = computed(() => {
  if (props.type === 'github') {
    return isMobile.value ? mobileGithubCarouselTexts : githubCarouselTexts
  }
  return isMobile.value ? mobileScholarCarouselTexts : scholarCarouselTexts
})

// Currently displayed placeholder
const currentPlaceholder = computed(() => {
  if (props.enableCarousel) {
    return carouselTexts.value[currentPlaceholderIndex.value]
  }
  return staticPlaceholder.value
})

// Static placeholder
const staticPlaceholder = computed(() => {
  if (props.placeholder) {
    return props.placeholder
  }

  if (props.type === 'github') {
    return 'Name or Github link'
  }

  return 'Name or Google Scholar link'
})



// Start carousel
const startCarousel = () => {
  if (!props.enableCarousel) return

  if (intervalId.value) {
    clearTimeout(intervalId.value)
  }

  const scheduleNext = () => {
    const currentText = carouselTexts.value[currentPlaceholderIndex.value]
    const delay = isHelpText(currentText) ? 5000 : 3000 // Hint text 5 seconds, others 3 seconds

    intervalId.value = setTimeout(() => {
      if (!isUserFocused.value && !searchValue.value.trim()) {
        currentPlaceholderIndex.value = (currentPlaceholderIndex.value + 1) % carouselTexts.value.length
        scheduleNext() // Recursively schedule next
      }
    }, delay)
  }

  scheduleNext()
}

// Stop carousel
const stopCarousel = () => {
  if (intervalId.value) {
    clearTimeout(intervalId.value)
    intervalId.value = null
  }
}

const handleEnterSearch = () => {
  if (searchValue.value.trim()) {
    emit('enterSearch', searchValue.value.trim())
  } else if (props.enableCarousel) {
    // If no input content and carousel is enabled, search current displayed placeholder
    const placeholderText = currentPlaceholder.value

    // If it's hint text, don't execute search
    if (isHelpText(placeholderText)) {
      return
    }

    // Directly use complete content (URL or name)
    const searchTerm = placeholderText.trim()
    emit('enterSearch', searchTerm)
  }
}

const handleFocus = () => {
  isUserFocused.value = true
  // Stop carousel when focused
  stopCarousel()
}

const handleBlur = () => {
  isUserFocused.value = false
  // Restart carousel when focus is lost
  if (props.enableCarousel) {
    startCarousel()
  }
}

const handleInput = () => {
  // Handle logic when user starts typing
}

// IME input method related event handling
const handleCompositionStart = () => {
  isComposing.value = true
}

const handleCompositionUpdate = () => {
  isComposing.value = true
}

const handleCompositionEnd = () => {
  isComposing.value = false
}

onMounted(() => {
  if (props.enableCarousel) {
    startCarousel()
  }
})

onUnmounted(() => {
  stopCarousel()
})

defineExpose({
  searchValue,
  currentPlaceholder
})
</script>

<style scoped>
.search-input-wrapper {
  position: relative;
  width: 100%;
}

.search-input-container {
  position: relative;
  display: flex;
  flex: 1;
}

.search-input {
  position: relative;
  z-index: 2;
}

.animated-placeholder,
.static-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  pointer-events: none;
  z-index: 1;
}

.placeholder-text {
  font-weight: normal;
  color: #7C8493;
  font-size: inherit;
}

/* 移动端字体大小调整 */
@media (max-width: 768px) {
  .search-input,
  .placeholder-text {
    font-size: 14px !important;
  }
}

@media (max-width: 480px) {
  .search-input,
  .placeholder-text {
    font-size: 12px !important;
  }
}


/* Carousel animation */
.placeholder-fade-enter-active,
.placeholder-fade-leave-active {
  transition: opacity 0.4s ease;
}

.placeholder-fade-enter-from,
.placeholder-fade-leave-to {
  opacity: 0;
}

.placeholder-fade-enter-to,
.placeholder-fade-leave-from {
  opacity: 1;
}





/* Dark mode support */
.dark .placeholder-text {
  color: #7C8493;
}
</style>
