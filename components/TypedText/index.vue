<template>
  <span class="typed-text-container">
    <span ref="typedText"></span>
    <span class="blinking-cursor">|</span>
  </span>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted, onBeforeUnmount } from 'vue'
  import Typed from 'typed.js'

  const props = defineProps<{
    strings: string[]
    typeSpeed?: number
    backSpeed?: number
    loop?: boolean
  }>()

  const typedText = ref<HTMLElement | null>(null)
  let typedInstance: Typed | null = null

  const initTyped = () => {
    if (typedText.value) {
      typedInstance = new Typed(typedText.value, {
        strings: props.strings,
        typeSpeed: props.typeSpeed || 50,
        backSpeed: props.backSpeed || 50,
        loop: props.loop || false,
        showCursor: false, // Disable default cursor, use custom cursor
      })
    }
  }

  const destroyTyped = () => {
    if (typedInstance) {
      typedInstance.destroy()
      typedInstance = null
    }
  }

  onMounted(() => {
    initTyped()
  })

  watch(
    () => props.strings,
    () => {
      destroyTyped()
      initTyped()
    }
  )

  onBeforeUnmount(() => {
    destroyTyped()
  })
</script>

<style scoped>
.typed-text-container {
  display: inline;
}

.blinking-cursor {
  animation: blink 1s infinite;
  font-weight: normal;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}
</style>
