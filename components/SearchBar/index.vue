<template>
  <motion.div
    :initial="{ opacity: 0, y: 10 }"
    :animate="{ opacity: 1, y: 0 }"
    :transition="{ duration: 0.3, ease: 'easeInOut', delay: 0.2 }"
    class="f-cer mt-7.5 mb-7"
  >
    <div
      :class="[
        'custom-input border rounded-full bg-white border-black p-1 flex items-center',
        isSmallScreen ? 'min-h-14 max-w-full gap-1 pl-4 pr-1' :
        (isMobile || isTablet) ? 'min-h-16 max-w-full gap-4 pl-7.5' : 'min-h-16 max-w-195 gap-4 pl-7.5',
        'w-full'
      ]"
    >
      <div :class="isSmallScreen ? 'flex-1 min-w-0 mr-1' : 'flex-1'">
        <SearchInput
          ref="searchInputRef"
          :placeholder="placeholderText"
          :type="props.type"
          :enable-carousel="true"
          @enter-search="handleSearch"
        />
      </div>
      <div :class="isSmallScreen ? 'flex-shrink-0 w-24' : 'flex-shrink-0'">
        <ActionButton @click="handleSearch" :imgSrc="magicWand" buttonText="Analyze" />
      </div>
    </div>
  </motion.div>
</template>

<script setup lang="ts">
  const props = defineProps({
    type: String,
  })

  import { motion } from 'motion-v'
  import magicWand from '@/assets/image/magic-wand.png'
  import { extractGitHubUsername } from '~/utils'
  const searchInputRef = ref()
  const router = useRouter()
  const route = useRoute()
  const { currentUser } = useFirebaseAuth()
  const { $emitter } = useNuxtApp()

  // 检测移动端和小屏幕
  const isMobile = ref(false)
  const isTablet = ref(false)
  const isSmallScreen = ref(false)

  onMounted(() => {
    const checkScreenSize = () => {
      isMobile.value = window.innerWidth <= 480
      isTablet.value = window.innerWidth <= 768 && window.innerWidth > 480
      isSmallScreen.value = window.innerWidth <= 500
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)

    onUnmounted(() => {
      window.removeEventListener('resize', checkScreenSize)
    })
  })

  // Compute placeholder text for small screens (fallback)
  const placeholderText = computed(() => {
    // 500px以下显示简化文字
    if (isSmallScreen.value) {
      return 'Search link…'
    }

    // For larger screens, let SearchInput handle the placeholder
    return undefined
  })

  // 判断是否为提示文本
  const isHelpText = (text: string) => {
    return text.includes('Enter ') || text.includes('Paste any ')
  }

  const handleSearch = (query?: string) => {
    let search = query || searchInputRef.value.searchValue

    // 如果没有搜索内容，尝试从轮播placeholder获取
    if (!search.trim() && searchInputRef.value) {
      // 获取当前显示的placeholder
      const currentPlaceholder = searchInputRef.value.currentPlaceholder
      if (currentPlaceholder) {
        // 如果是提示文本，不执行搜索
        if (isHelpText(currentPlaceholder)) {
          // 如果没有登录，仍然提示登录（让用户知道需要登录才能使用）
          if (!currentUser.value) {
            $emitter.emit('auth')
          }
          return
        }

        // 直接使用完整内容（URL或人名）
        search = currentPlaceholder.trim()
      }
    }

    if (!search.trim()) {
      // 如果没有登录，仍然提示登录（让用户知道需要登录才能使用）
      if (!currentUser.value) {
        $emitter.emit('auth')
      }
      return
    }

    // 检查用户是否登录
    if (!currentUser.value) {
      $emitter.emit('auth')
      return
    }

    // 执行搜索
    if(props.type === 'scholar') {
      router.push(`/report?query=${search}`)
    }
    if(props.type === 'github') {
      // Extract GitHub username from URL or use as-is if it's already a username
      const username = extractGitHubUsername(search)
      router.push(`/github?user=${username}`)
    }
  }

  // const emit = defineEmits(['analyze'])
</script>

<style scoped>
/* 桌面端固定搜索框宽度 */
@media (min-width: 1024px) {
  .custom-input {
    width: 760px !important;
    max-width: 760px !important;
  }
}

/* 平板端搜索框宽度优化 */
@media (max-width: 768px) and (min-width: 481px) {
  .custom-input {
    width: 100% !important;
    max-width: 100% !important;
  }
}

/* 手机端搜索框宽度优化 */
@media (max-width: 480px) {
  .custom-input {
    width: 100% !important;
    max-width: 100% !important;
  }
}

/* 确保在小屏幕下按钮不会溢出 */
@media (max-width: 500px) {
  .custom-input {
    overflow: hidden; /* 防止内容溢出 */
  }
  
  /* 在小屏幕下限制SearchInput的flex行为 */
  .custom-input :deep(.fx-cer) {
    width: 100% !important;
    flex: none !important;
  }
  
  /* 确保按钮大小适配小屏幕 */
  .custom-input :deep(.action-button) {
    padding: 10px 12px !important; /* 调整按钮内边距 */
    font-size: 14px !important; /* 稍微减小字体 */
    white-space: nowrap !important; /* 防止文字换行 */
    min-height: 44px !important; /* 确保按钮有足够高度 */
    height: 44px !important; /* 固定按钮高度 */
  }
}
</style>
