<template>
  <div class="px-6 py-12 max-w-7xl mx-auto">
    <!-- 如果有选中博客，展示详情页 -->
    <div v-if="selectedPost">
      <BlogDetail :post="selectedPost" @back="backToList" />
    </div>

    <!-- 否则展示列表 -->
    <div v-else>
      <h1 class="text-center text-[70px] font-bold mb-2 mt-10">Blog</h1>
      <p class="w-[320px] text-center text-4 text-[#757575] mb-10 mx-auto">
        Product updates, insights, and behind-the-scenes from the DINQ team.
      </p>

      <!-- 博客卡片区域 -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        <BlogCard v-for="post in posts" :key="post.id" :post="post" @click="onBlogClick(post)" />
      </div>

      <!-- 分页 -->
      <div class="flex justify-center">
        <BlogPagination :total="total" :page="page" :limit="limit" @change="onPageChange" />
      </div>
    </div>

    <!-- 页脚内容 - 移出section-container，独立成为全宽区域 -->
    <footer class="landing-footer">
      <div class="footer-main-content">
        <div class="footer-top">
          <div class="footer-logo">
            <nuxt-link to="/">
              <img src="/image/newlogo1.png" width="95" height="42" alt="DINQ logo" />
            </nuxt-link>
          </div>

          <div class="footer-right">
            <div class="footer-links">
              <a href="/terms" class="footer-link">
                <span>Terms & Conditions</span>
              </a>
              <a href="/privacy" class="footer-link">
                <span>Privacy Policy</span>
              </a>
            </div>
            <div class="footer-social">
              <a href="https://x.com/dinq_io" target="_blank">
                <button class="media-btn border-none !bg-twitter">
                  <div class="i-proicons:x-twitter wh-6"></div>
                </button>
              </a>
              <a href="https://discord.gg/JyQwmYUTM6" target="_blank">
                <button class="discord-btn">
                  <div class="text-base"></div>
                </button>
              </a>
            </div>
          </div>
        </div>

        <div class="footer-divider"></div>

        <div class="footer-copyright">Copyright© 2025 DINQ Inc. All Rights Reserved</div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch } from 'vue'
  import BlogCard from './BlogCard.vue'
  import BlogPagination from './BlogPagination.vue'
  import BlogDetail from './BlogDetail.vue'
  import { fetchBlogPosts } from './api'

  const posts = ref<any[]>([])
  const page = ref(1)
  const limit = 9
  const total = ref(0)

  // 新增：当前选中的博客（用于展示详情）
  const selectedPost = ref<any | null>(null)

  const fetchPosts = async () => {
    const res = await fetchBlogPosts(page.value, limit)
    posts.value = res.posts
    total.value = res.total
  }

  onMounted(fetchPosts)
  watch(page, fetchPosts)

  const onPageChange = (newPage: number) => {
    page.value = newPage
  }

  // 点击博客卡片事件
  const onBlogClick = (post: any) => {
    selectedPost.value = post
  }

  // 返回列表事件
  const backToList = () => {
    selectedPost.value = null
  }
</script>

<style scoped>
  /* 页脚 */
  .landing-footer {
    margin-top: 30px; /* mt-7.5 = 1.875rem = 30px */
    padding: 30px 0;
    width: 100%; /* 确保页脚横跨整个屏幕 */
    position: relative;
    left: 0;
    right: 0;
  }

  .footer-main-content {
    /* 移除最大宽度限制，让页脚内容横跨整个屏幕 */
    width: 100%;
    padding: 0 24px;
    box-sizing: border-box;
  }

  .footer-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
  }

  .footer-right {
    display: flex;
    align-items: center;
    gap: 80px; /* gap-20 = 5rem = 80px */
  }

  .footer-links {
    display: flex;
    align-items: center;
    gap: 46px; /* gap-[2.88rem] = 46px */
  }

  .footer-link {
    font-family:
      Poppins,
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      Oxygen,
      Ubuntu,
      Cantarell,
      sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 150%;
    color: #283646;
    text-decoration: none;
    text-transform: capitalize;
    transition: color 0.3s ease;
  }

  .footer-link:hover {
    color: #2563eb;
  }

  .footer-social {
    display: flex;
    align-items: center;
    gap: 22px; /* gap-5.5 = 1.375rem = 22px */
  }

  .media-btn {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: white;
  }

  .discord-btn {
    border-radius: 50%;
    width: 42px;
    height: 42px;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
    background-color: #ffffff;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    cursor: pointer;
  }

  .discord-btn:hover {
    background-color: #f3f4f6;
    color: #cb7c5d;
  }

  .text-base {
    background-image: url('~/assets/image/footthemedark.png');
    background-size: 100%;
    width: 42px;
    height: 42px;
    display: inline-block;
  }

  .footer-divider {
    width: 100%;
    height: 1px;
    background-color: #e0e6ee;
    margin: 30px 0 20px 0;
  }

  .footer-copyright {
    text-align: center;
    font-family:
      Poppins,
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      Oxygen,
      Ubuntu,
      Cantarell,
      sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 150%;
    color: #283646;
    text-transform: capitalize;
  }
</style>
