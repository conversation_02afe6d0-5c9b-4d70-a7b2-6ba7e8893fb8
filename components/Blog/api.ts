export const fetchBlogPosts = async (page = 1, limit = 9) => {
  const total = 50

  const posts = Array.from({ length: limit }).map((_, index) => {
    const id = (page - 1) * limit + index + 1
    return {
      id,
      title: `DINQ’s AI-powered agent rapidly and accurately identifies elite AI tale`,
      image: '~/assets/image/blog.png', // static image
      author: 'DINQ Team',
      date: '2023-07-01',
      description: "DINQ is an intelligent platform designed to help you analyze, compare, or discover AI researchers in under 60 seconds. Whether you're evaluating a scholar's work, benchmarking experts, or searching for collaborators, DINQ simplifies the process with quick, actionable insights.",
      credits: ['Free Plan: 5 credits/month (automatically replenished).', 'Standard Subscription: 50 credits/month.', 'Premium Subscription: 150 credits/month.', 'For pricing details and benefits, visit our Subscription Page.']
    }
  })

  return {
    total,
    posts,
  }
}
