<template>
  <div class="bg-white rounded shadow hover:shadow-lg transition duration-300 overflow-hidden" @click="$emit('click')">
    <!-- <img :src="post.image" alt="" class="w-full h-48 object-cover" /> -->
    <img src="~/assets/image/blog.png" alt="" class="w-full h-48 object-cover" />
    <div class="p-4">
      <p class="font-semibold line-clamp-2 mb-2">{{ post.title }}</p>
      <div class="text-sm text-gray-500 flex items-center gap-1">
        <span class="font-bold fx-cer gap-2"><img src="~/assets/image/logo-small.svg" alt="" class="w-8 h-8"></span> <span>{{ post.author }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  post: {
    id: number
    image: string
    title: string
    author: string
  }
}>()
defineEmits(['click'])
</script>
