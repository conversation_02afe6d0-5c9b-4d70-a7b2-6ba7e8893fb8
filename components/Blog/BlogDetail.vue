<template>
  <div class="w-250 mx-auto">
    <div class="fx-cer justify-center mt-20">
      <img src="~/assets/image/blogBg.png" alt="cover" class="w-[1000px] h-[400px] mb-6" />
    </div>
    <h2 class="text-4xl font-bold mb-4">{{ post.title }}</h2>
    <div class="h-[60px] flex items-center gap-2 justify-between">
      <div class="flex items-center gap-2">
        <span class="font-bold fx-cer gap-2">
          <img src="~/assets/image/logo-small.svg" alt="" class="w-8 h-8" />
        </span>
        <span>{{ post.author }}</span>
      </div>
      <span>{{ post.date }}</span>
    </div>
    <div class="text-4 font-400">{{ post.description }}</div>
    <div class="fx-cer justify-center my-4">
      <img
        src="~/assets/image/blog.png"
        alt="cover"
        class="w-[650px] h-[400px]"
        v-if="post.image"
      />
    </div>

    <div class="text-4 font-400">
      <div>Credits are available through:</div>
      <div v-for="(credit, index) in post.credits" :key="index">
        {{ credit }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  defineProps<{ post: any }>()
  defineEmits(['back'])
</script>
