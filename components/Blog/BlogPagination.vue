<template>
  <nav class="flex gap-1 items-center">
    <!-- 上一页按钮 -->
    <button class="px-3 py-1 border rounded" @click="changePage(props.page - 1)" :disabled="props.page === 1">&lt;</button>

    <!-- 分页按钮 -->
    <template v-for="item in displayPages" :key="item.key">
      <button
        v-if="!item.ellipsis"
        class="px-3 py-1 border rounded"
        :class="{ 'border-[#CB7C5D] text-[#CB7C5D]': item.page === props.page }"
        @click="changePage(item.page)"
      >
        {{ item.page }}
      </button>
      <span v-else class="px-2">...</span>
    </template>

    <!-- 下一页按钮 -->
    <button class="px-3 py-1 border rounded" @click="changePage(props.page + 1)" :disabled="props.page === maxPage">&gt;</button>
  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  total: number
  page: number
  limit: number
}>()

const emit = defineEmits<{
  (e: 'change', page: number): void
}>()

const maxPage = computed(() => Math.ceil(props.total / props.limit))

const changePage = (p: number) => {
  if (p !== props.page && p >= 1 && p <= maxPage.value) {
    emit('change', p)
  }
}

const displayPages = computed(() => {
  const pages = []
  const { page } = props
  const max = maxPage.value

  if (max <= 7) {
    for (let i = 1; i <= max; i++) {
      pages.push({ page: i, key: `page-${i}` })
    }
    return pages
  }

  // Always show first page
  pages.push({ page: 1, key: 'page-1' })

  // Left ellipsis
  if (page > 4) {
    pages.push({ ellipsis: true, key: 'ellipsis-left' })
  }

  // Middle pages (current ±2)
  const start = Math.max(2, page - 2)
  const end = Math.min(max - 1, page + 2)
  for (let i = start; i <= end; i++) {
    pages.push({ page: i, key: `page-${i}` })
  }

  // Right ellipsis
  if (page < max - 3) {
    pages.push({ ellipsis: true, key: 'ellipsis-right' })
  }

  // Always show last page
  pages.push({ page: max, key: `page-${max}` })

  return pages
})
</script>
