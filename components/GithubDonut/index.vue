<template>
  <div ref="chartRef" class="w-full h-full">
    <div v-if="!hasData" class="flex items-center justify-center h-full text-gray-500">
      No language data available
    </div>
  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { onMounted, ref, onBeforeUnmount, computed, watch } from 'vue'

// Format numbers, display as M unit when over 1M
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  return num.toLocaleString()
}

// Props for GitHub languages data
const props = defineProps({
  languages: {
    type: Object,
    default: () => ({})
  },
  total: {
    type: Number,
    default: null
  },
  compact: {
    type: Boolean,
    default: false
  }
})

// Custom color scheme
const lightColors = ['#7F95CE', '#F8E9C8', '#CB7C5D', '#D2CEC4', '#B89EDA', '#A8C8A8']
const darkColors = ['#5A6275', '#817968', '#654D43', '#919191', '#6B5B95', '#5A7A5A']

// Chart DOM
const chartRef = ref<HTMLDivElement | null>(null)
let chart: echarts.ECharts | null = null
let isComponentMounted = false

// Detect dark mode
const isDark = ref(false)

// Choose colors based on dark mode
const colors = computed(() => isDark.value ? darkColors : lightColors)

// Validate if value is valid
const isValidNumber = (value: any): boolean => {
  return typeof value === 'number' && !isNaN(value) && isFinite(value) && value >= 0
}

// Check if there is data
const hasData = computed(() => {
  const languageEntries = Object.entries(props.languages || {})
  return languageEntries.length > 0 && languageEntries.some(([, value]) => isValidNumber(value) && value > 0)
})

// Process language data
const processedData = computed(() => {
  const languageEntries = Object.entries(props.languages || {})

  if (languageEntries.length === 0) {
    return []
  }

  // Validate and filter valid data
  const validLanguages = languageEntries.filter(([, value]) => isValidNumber(value) && value > 0)
  
  if (validLanguages.length === 0) {
    return []
  }

  // Sort by value and take top languages
  const sortedLanguages = validLanguages.sort(([,a], [,b]) => b - a)

  // Take top 5 languages
  const topLanguages = sortedLanguages.slice(0, 5)
  
  // If there are more than 5 languages, group the rest as "Other"
  const remainingLanguages = sortedLanguages.slice(5)
  const otherTotal = remainingLanguages.reduce((sum, [, value]) => sum + value, 0)

  const result = topLanguages.map(([name, value]) => ({ name, value }))
  
  if (otherTotal > 0) {
    result.push({ name: 'Other', value: otherTotal })
  }

  return result
})

// Total - prioritize passed total, otherwise calculate sum of languages
const total = computed(() => {
  if (props.total !== null && props.total !== undefined) {
    return props.total
  }
  return processedData.value.reduce((sum, item) => sum + item.value, 0)
})

// Construct series data with label styles
const seriesData = computed(() => processedData.value.map((item, index) => ({
  ...item,
  label: {
    show: true, // Always show external labels
    position: 'outside',
    formatter: (params: any) => {
      const { name, value, percent } = params
      return `{bar|}\n{name|${name}}\n{value|${formatNumber(value)}}{percent|${percent}%}`
    },
    backgroundColor: isDark.value ? '#2A2A2A' : '#FAF2EF',
    borderRadius: 0,
    padding: [8, 10, 8, 10], // Increase padding to make labels more beautiful
    fontSize: 12,
    fontFamily: 'Poppins',
    color: isDark.value ? '#FFFFFF' : '#030229',
    width: 100, // Limit maximum label width
          rich: {
        bar: {
          height: 5,
          width: '100%',
          backgroundColor: colors.value[index % colors.value.length],
        },
      name: {
        fontSize: 14, // Increase font size
        fontFamily: 'Poppins',
        align: 'center', // Center align
        padding: [4, 8, 4, 8], // Adjust padding
        lineHeight: 18, // Increase line height
      },
      value: {
        fontSize: 12,
        fontFamily: 'Poppins',
        align: 'left', // Left align
        padding: [2, 8, 4, 0], // Adjust padding
        lineHeight: 16,
      },
      percent: {
        fontSize: 12,
        fontFamily: 'Poppins',
        align: 'right', // Right align
        padding: [2, 0, 4, 8], // Adjust padding
        lineHeight: 16,
      },
    },
  },
})))

const option = computed(() => ({
  color: colors.value,
  backgroundColor: 'transparent',
  legend: {
    show: !props.compact, // Control legend display based on compact property: compact mode (share card) doesn't show legend, normal mode shows legend
    bottom: 0,
    itemWidth: 16, // Increase legend width
    itemHeight: 16, // Increase legend height
    icon: 'roundRect',
    textStyle: {
      fontSize: 12, // Increase legend text size
      fontFamily: 'Poppins',
      color: isDark.value ? '#FFFFFF' : '#030229',
    },
  },
  series: [
    {
      type: 'pie',
      radius: ['45%', '65%'], // Increase circle size
      avoidLabelOverlap: false,
      labelLine: {
        show: true,
        length: 12, // Shorten first segment of guide line
        length2: 8,  // Shorten second segment of guide line
        lineStyle: {
          color: isDark.value ? '#666' : '#ccc',
          width: 1,
        },
        smooth: true,
      },
      data: seriesData.value,
    },
  ],
  graphic: props.compact ? [
    {
      type: 'text',
      left: 'center',
      top: '42%',
      style: {
        text: formatNumber(total.value), // Display total code count
        textAlign: 'center',
        fill: isDark.value ? '#FFFFFF' : '#030229',
        fontSize: 18,
        fontWeight: 600,
        fontFamily: 'Poppins',
      },
    },
    {
      type: 'text',
      left: 'center',
      top: '52%',
      style: {
        text: 'Total Code',
        textAlign: 'center',
        fill: isDark.value ? '#A0A0A0' : '#969696',
        fontSize: 11,
        fontWeight: 500,
        fontFamily: 'Poppins',
      },
    },
  ] : [
    {
      type: 'text',
      left: 'center',
      top: '42%',
      style: {
        text: formatNumber(total.value), // Format total count
        textAlign: 'center',
        fill: isDark.value ? '#FFFFFF' : '#030229',
        fontSize: 22,
        fontWeight: 600,
        fontFamily: 'Poppins',
      },
    },
    {
      type: 'text',
      left: 'center',
      top: '52%',
      style: {
        text: 'Total Code',
        textAlign: 'center',
        fill: isDark.value ? '#A0A0A0' : '#030229',
        fontSize: 11,
        fontFamily: 'Poppins',
      },
    },
  ],
}))

// Detect dark mode changes - only execute on client side
const updateDarkMode = () => {
  if (process.client) {
    isDark.value = document.documentElement.classList.contains('dark')
  }
}

const updateChart = () => {
  if (chart && hasData.value && isComponentMounted) {
    chart.setOption(option.value, true)
  }
}

onMounted(() => {
  isComponentMounted = true

  // Initialize dark mode detection
  updateDarkMode()

  // Listen for dark mode changes
  const observer = new MutationObserver(() => {
    updateDarkMode()
    updateChart() // Update chart when dark mode changes
  })
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  })

  if (chartRef.value && hasData.value) {
    chart = echarts.init(chartRef.value)
    updateChart()
  }
})

// Watch for data changes
watch(() => props.languages, () => {
  if (!isComponentMounted) return
  
  if (hasData.value) {
    if (!chart && chartRef.value) {
      chart = echarts.init(chartRef.value)
    }
    updateChart()
  } else if (chart) {
    chart.dispose()
    chart = null
  }
}, { deep: true })

// Watch for dark mode changes
watch(isDark, () => {
  updateChart()
})

onBeforeUnmount(() => {
  isComponentMounted = false
  if (chart) {
    chart.dispose()
    chart = null
  }
})
</script>

<style scoped>
div {
  width: 100%;
  height: 100%;
  min-height: 180px;
}
</style>
