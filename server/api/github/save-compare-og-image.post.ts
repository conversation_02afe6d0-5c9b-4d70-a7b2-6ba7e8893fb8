export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { user1, user2, ogImageUrl } = body

    if (!user1 || !user2 || !ogImageUrl) {
      throw createError({
        statusCode: 400,
        statusMessage: 'user1, user2 and ogImageUrl are required'
      })
    }

    // TODO: Implement database logic to save comparison page OG image URL
    // Example: await db.compareOgImage.upsert({
    //   where: { user1_user2: `${user1}_${user2}` },
    //   create: { user1, user2, ogImageUrl },
    //   update: { ogImageUrl }
    // })

    // Temporarily return success, actual implementation needs database connection
    console.log(`Saving compare OG image URL for ${user1} vs ${user2}: ${ogImageUrl}`)
    
    return {
      success: true,
      message: 'Compare OG image URL saved successfully'
    }
  } catch (error) {
    console.error('Error saving compare OG image URL:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to save compare OG image URL'
    })
  }
})
