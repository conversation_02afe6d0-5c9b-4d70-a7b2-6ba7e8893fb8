export async function onRequest(context) {
  const { request } = context;
  const url = new URL(request.url);

  // 构建目标 URL
  const targetUrl = `https://search.dinq.io/api/v1/talent/credits${url.search}`;

  // 转发请求
  const response = await fetch(targetUrl, {
    method: request.method,
    headers: request.headers,
    body: request.body
  });

  // 添加CORS头部
  const newResponse = new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: {
      ...response.headers,
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, userid',
    }
  });

  return newResponse;
}
