// src/api/recommend.ts
import type {
    NewRecommendPapersResponse,
  } from './types'; // Import from local types file
  
// 直接返回硬编码的API基础URL
const getApiBaseUrl = () => {
  return 'https://api.dinq.io'
}

// 构建完整的API URL - 统一使用代理方式
const getRecommendApiUrl = () => {
  // 由于 recommend 接口不支持跨域，统一使用代理路径
  return '/api/v1/recommend'
}
  
  async function baseFetch<T>(url: string, options: RequestInit): Promise<T> {
    const response = await fetch(url, options);
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: `HTTP error! Status: ${response.status}` }));
      throw new Error(errorData.message || `HTTP error! Status: ${response.status}`);
    }
    return response.json() as Promise<T>;
  }
  
  // Updated interface for GET parameters
  export interface RecommendPapersParams {
    text: string; // 更新参数名从query到text
    query?: string; // 保持向后兼容性
    first_author_only?: number; // 0 or 1, defaults to 0
    user?: string; // 用户ID，用于鉴权
    code?: string; // 调试模式代码，用于避免请求次数限制
  }
  
  export const recommendPapers = async (
    params: RecommendPapersParams,
    headers?: Record<string, string>
  ): Promise<NewRecommendPapersResponse> => {
    // Build query string
    const searchParams = new URLSearchParams();
    // 使用新的text参数，向后兼容query参数
    const queryText = params.text || params.query;
    if (queryText) {
      searchParams.append('text', queryText);
    }
    if (params.first_author_only !== undefined) {
      searchParams.append('first_author_only', params.first_author_only.toString());
    }
    if (params.user) {
      searchParams.append('user', params.user);
    }
    if (params.code) {
      searchParams.append('code', params.code);
    }
    
    // 使用动态URL构建
    const baseUrl = getRecommendApiUrl()
    const url = `${baseUrl}?${searchParams.toString()}`;
    console.log(url)

    
    try {
      // 创建AbortController用于超时控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
      }, 30000); // 30秒超时
      
      const response = await fetch(url, {
      method: 'GET',
        headers: headers || {},
        signal: controller.signal,
      });
      
      // 清除超时定时器
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error! Status: ${response.status}, Message: ${errorText}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error: any) {
      // 特殊处理不同类型的错误
      if (error?.name === 'AbortError') {
        throw new Error('Request timeout after 30 seconds');
      } else if (error?.name === 'TypeError') {
        throw new Error('Network error: ' + error.message);
      }
      
      throw error;
    }
  };