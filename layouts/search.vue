<template>
  <div class="search-layout">
    <!-- Search页面专用布局 - 不包含AppHeader和AppFooter -->
    <main class="search-main">
      <slot />
    </main>
  </div>
</template>

<script setup lang="ts">
import { useFirebaseAuth } from '~/composables/useFirebaseAuth'
// 保持Firebase认证功能
useFirebaseAuth()
</script>

<style scoped>
.search-layout {
  min-height: 100vh;
  width: 100%;
  background-color: #EEEEEA;
  transition: background-color 0.3s ease;
}

/* 深色模式支持 */
.dark .search-layout {
  background-color: #0F0F0F;
}

.search-main {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}
</style>
