# 动态 SEO 元数据示例

## 🎯 功能说明
现在 GitHub 页面和 Compare 页面都支持动态 SEO 元数据了！当用户访问这些页面时，服务器会根据实际的用户数据生成个性化的 meta 标签。

## 📝 实现的动态元数据

### GitHub 个人页面 (`/github?user=username`)
```html
<!-- 动态标题 -->
<title>John <PERSON> - GitHub Developer Profile | DINQ</title>

<!-- 动态描述 -->
<meta name="description" content="<PERSON> is a developer with 50 repositories and 1200 GitHub stars. Expert in JavaScript, Python, TypeScript." />

<!-- 动态关键词 -->
<meta name="keywords" content="<PERSON>, GitHub Developer, Software Engineer, JavaScript, Python, TypeScript, React, Node.js" />

<!-- Open Graph -->
<meta property="og:title" content="John Doe - GitHub Developer Profile" />
<meta property="og:description" content="<PERSON> is a developer with 50 repositories and 1200 GitHub stars..." />
<meta property="og:image" content="https://github.com/johndoe.png" />
<meta property="og:url" content="https://dinq.io/github?user=johndoe" />

<!-- Twitter Card -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="John Doe - GitHub Developer Profile" />
<meta name="twitter:image" content="https://github.com/johndoe.png" />

<!-- 结构化数据 -->
<meta property="profile:first_name" content="John" />
<meta property="profile:last_name" content="Doe" />
<meta property="profile:username" content="johndoe" />
```

### GitHub 比较页面 (`/github/compare?user1=alice&user2=bob`)
```html
<!-- 动态标题 -->
<title>Alice Smith vs Bob Johnson - GitHub Developer Comparison | DINQ</title>

<!-- 动态描述 -->
<meta name="description" content="Compare GitHub developers Alice Smith and Bob Johnson. Analyze their coding skills, project contributions, and development expertise side by side." />

<!-- 动态关键词 -->
<meta name="keywords" content="Alice Smith, Bob Johnson, GitHub Comparison, Developer Comparison, JavaScript, Python, React, Vue.js" />

<!-- Open Graph -->
<meta property="og:title" content="Alice Smith vs Bob Johnson - GitHub Developer Comparison" />
<meta property="og:url" content="https://dinq.io/github/compare?user1=alice&user2=bob" />
```

### 学者比较页面 (`/compare?researcher1=name1&researcher2=name2`)
```html
<!-- 动态标题 -->
<title>Dr. Jane Smith vs Prof. Mike Chen - Scholar Comparison | DINQ</title>

<!-- 动态描述 -->
<meta name="description" content="Compare researchers Dr. Jane Smith and Prof. Mike Chen. Analyze their academic achievements, publications, citations, and research impact." />

<!-- 动态关键词 -->
<meta name="keywords" content="Dr. Jane Smith, Prof. Mike Chen, Scholar Comparison, Machine Learning, Computer Vision, AI Research" />
```

## 🚀 SEO 优势

### 1. 个性化内容
- 每个用户页面都有独特的标题和描述
- 基于实际数据生成关键词
- 包含用户的真实头像和信息

### 2. 社交媒体优化
- 完整的 Open Graph 标签支持
- Twitter Card 优化
- 动态生成分享图片

### 3. 搜索引擎友好
- 语义化的 meta 标签
- 结构化数据支持
- 规范化 URL (canonical)

### 4. 实时更新
- 数据获取成功后立即更新 SEO 信息
- 服务端渲染确保搜索引擎能抓取到完整内容

## 🔧 技术实现

### 核心函数
```typescript
// GitHub 个人页面
const updateSeoMeta = (data: GitHubAnalysisData) => {
  const userName = data.user.name || data.user.login
  const description = data.description || `${userName} is a developer...`
  
  useSeoMeta({
    title: `${userName} - GitHub Developer Profile | DINQ`,
    description: description.slice(0, 160),
    ogImage: data.user.avatarUrl,
    // ... 更多 meta 标签
  })
}

// 在数据获取成功后调用
if (githubData.value) {
  updateSeoMeta(githubData.value)
}
```

### 调用时机
- ✅ 数据获取成功后立即更新
- ✅ 服务端渲染时生成
- ✅ 客户端路由切换时更新

## 📊 预期效果

### 搜索引擎
- Google 搜索结果中显示个性化标题和描述
- 更好的搜索排名和点击率
- 丰富的搜索结果片段

### 社交分享
- 在 Twitter、LinkedIn 等平台分享时显示正确的卡片
- 包含用户头像和个性化描述
- 提升分享的视觉效果和点击率

### 用户体验
- 浏览器标签页显示有意义的标题
- 书签保存时有清晰的页面标识
- 更专业的品牌形象

这就是 SSR 的强大之处 - 能够根据实际数据动态生成 SEO 优化的内容！🎉
