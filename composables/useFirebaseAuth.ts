import { ref } from 'vue';
import {
  signInWithPopup,
  signOut,
  onAuthStateChanged,
  type User,
  setPersistence,
  browserLocalPersistence,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  sendPasswordResetEmail,
  sendEmailVerification,
  updateProfile,
  reload
} from 'firebase/auth';
import { googleProvider, createOptimizedGoogleProvider, isSafari } from '@/utils/authProviders';

// 定义可序列化的用户类型
interface SerializableUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
}

// 转换 Firebase User 为可序列化对象
const convertToSerializable = (user: User | null): SerializableUser | null => {
  if (!user) return null;
  return {
    uid: user.uid,
    email: user.email,
    displayName: user.displayName,
    photoURL: user.photoURL,
    emailVerified: user.emailVerified,
  };
};

// Firebase 错误信息处理函数
const getFirebaseErrorMessage = (errorCode: string, errorMessage: string): string => {
  switch (errorCode) {
    case 'auth/user-not-found':
      return 'No account found with this email address. Please check your email or sign up for a new account.';
    case 'auth/wrong-password':
      return 'Incorrect password. Please try again or reset your password.';
    case 'auth/email-already-in-use':
      return 'An account with this email address already exists. Please sign in instead.';
    case 'auth/weak-password':
      return 'Password should be at least 6 characters long.';
    case 'auth/invalid-email':
      return 'Please enter a valid email address.';
    case 'auth/too-many-requests':
      return 'Too many failed attempts. Please try again later or reset your password.';
    case 'auth/user-disabled':
      return 'This account has been disabled. Please contact support.';
    case 'auth/operation-not-allowed':
      return 'Email/password accounts are not enabled. Please contact support.';
    case 'auth/invalid-credential':
      return 'Invalid email or password. Please check your credentials and try again.';
    case 'auth/network-request-failed':
      return 'Network error. Please check your internet connection and try again.';
    default:
      // 如果是未知错误，返回原始错误信息
      return errorMessage || 'An unexpected error occurred. Please try again.';
  }
};

// 跟踪全局监听器状态
let unsubscribe: (() => void) | null = null;
// 跟踪初始化状态
let isInitialized = false;

export const useFirebaseAuth = () => {
  const nuxtApp = useNuxtApp();
  const auth = nuxtApp.$auth;

  // 使用 useState 创建组件级状态
  const currentUser = useState<SerializableUser | null>('firebase-user', () => null);
  const loading = ref(false);
  const error = ref<string | null>(null);
  // 添加认证初始化状态
  const authInitialized = useState<boolean>('firebase-auth-initialized', () => false);

  // 只在客户端初始化一次监听
  if (process.client && !unsubscribe && auth && !isInitialized) {
    isInitialized = true;
    
    // 设置持久化登录
    setPersistence(auth, browserLocalPersistence)
      .then(() => {
        console.log('Auth persistence set to LOCAL');
      })
      .catch((err) => {
        console.error('Error setting auth persistence:', err);
      });
    
    // 设置认证状态监听 - 这是获取认证状态的唯一可靠方式
    unsubscribe = onAuthStateChanged(auth, (user) => {
      console.log('Auth state changed:', user ? 'User logged in' : 'User logged out');
      // 无论如何都更新状态，包括从 null 到 user 或从 user 到 null
      currentUser.value = convertToSerializable(user);
      // 标记认证已初始化
      authInitialized.value = true;
    });

    // 确保在页面离开时清理
    if (process.client) {
      window.addEventListener('beforeunload', () => {
        if (unsubscribe) {
          unsubscribe();
          unsubscribe = null;
          isInitialized = false;
        }
      });
    }
  }

  const loginWithGoogle = async () => {
    if (!auth) {
      console.error('Auth is not initialized');
      return;
    }

    loading.value = true;
    try {
      // 使用优化的provider，针对不同浏览器进行配置
      const provider = createOptimizedGoogleProvider();

      console.log(`Logging in with Google on ${isSafari() ? 'Safari' : 'other browser'}`);

      const result = await signInWithPopup(auth, provider);
      currentUser.value = convertToSerializable(result.user);
      error.value = null;
      return convertToSerializable(result.user);
    } catch (err: any) {
      console.error('Google login error:', err);
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const loginWithEmail = async (email: string, password: string) => {
    if (!auth) {
      console.error('Auth is not initialized');
      return;
    }

    loading.value = true;
    try {
      console.log('Logging in with email:', email);

      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      // Signed in
      const user = userCredential.user;
      currentUser.value = convertToSerializable(user);
      error.value = null;
      console.log('Email login successful for user:', user.uid);
      return convertToSerializable(user);
    } catch (err: any) {
      const errorCode = err.code;
      const errorMessage = err.message;
      console.error('Email login error:', { errorCode, errorMessage });
      error.value = getFirebaseErrorMessage(errorCode, errorMessage);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const registerWithEmail = async (email: string, password: string, displayName?: string, autoSendVerification: boolean = true) => {
    if (!auth) {
      console.error('Auth is not initialized');
      return;
    }

    loading.value = true;
    try {
      console.log('Registering with email:', email);

      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      // Signed up - 创建新账号后，用户会自动登录
      const user = userCredential.user;

      // 如果提供了 displayName，更新用户资料
      if (displayName && displayName.trim()) {
        try {
          await updateProfile(user, {
            displayName: displayName.trim()
          });
          console.log('User profile updated with displayName:', displayName);
        } catch (profileError) {
          console.warn('Failed to update user profile:', profileError);
          // 不抛出错误，因为注册本身是成功的
        }
      }

      currentUser.value = convertToSerializable(user);
      error.value = null;
      console.log('Email registration successful for user:', user.uid);

      // 自动发送邮箱验证邮件
      if (autoSendVerification) {
        try {
          await sendEmailVerification(user);
          console.log('Verification email sent automatically after registration');
        } catch (verificationError) {
          console.warn('Failed to send verification email after registration:', verificationError);
          // 不抛出错误，因为注册本身是成功的
        }
      }

      return convertToSerializable(user);
    } catch (err: any) {
      const errorCode = err.code;
      const errorMessage = err.message;
      console.error('Email registration error:', { errorCode, errorMessage });
      error.value = getFirebaseErrorMessage(errorCode, errorMessage);
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const resetPassword = async (email: string) => {
    if (!auth) {
      console.error('Auth is not initialized');
      return;
    }

    try {
      console.log('Sending password reset email to:', email);
      await sendPasswordResetEmail(auth, email);
      error.value = null;
      console.log('Password reset email sent successfully to:', email);
      return true;
    } catch (err: any) {
      const errorCode = err.code;
      const errorMessage = err.message;
      console.error('Password reset error:', { errorCode, errorMessage });
      error.value = getFirebaseErrorMessage(errorCode, errorMessage);
      throw err;
    }
  };

  const sendVerificationEmail = async () => {
    if (!auth || !auth.currentUser) {
      console.error('Auth is not initialized or no user is signed in');
      return;
    }

    try {
      console.log('Sending email verification to:', auth.currentUser.email);
      await sendEmailVerification(auth.currentUser);
      error.value = null;
      console.log('Email verification sent successfully');
      return true;
    } catch (err: any) {
      const errorCode = err.code;
      const errorMessage = err.message;
      console.error('Email verification error:', { errorCode, errorMessage });
      error.value = getFirebaseErrorMessage(errorCode, errorMessage);
      throw err;
    }
  };

  // 注意：邮箱验证由 Firebase 托管页面处理 (auth.dinq.io)
  // 用户点击邮件中的验证链接会跳转到 Firebase 的验证页面，无需自定义处理

  // 刷新用户的邮箱验证状态
  const refreshEmailVerificationStatus = async () => {
    if (!auth || !auth.currentUser) {
      console.error('Auth is not initialized or no user is signed in');
      return;
    }

    try {
      await reload(auth.currentUser);
      currentUser.value = convertToSerializable(auth.currentUser);
      console.log('Email verification status refreshed');
      return auth.currentUser.emailVerified;
    } catch (err: any) {
      console.error('Error refreshing email verification status:', err);
      throw err;
    }
  };



  const logout = async () => {
    if (!auth) {
      console.error('Auth is not initialized');
      return;
    }

    try {
      await signOut(auth);
      currentUser.value = null;
    } catch (err: any) {
      console.error('Logout error:', err);
      error.value = err.message;
      throw err;
    }
  };

  // 获取原始 Firebase User 对象（仅在需要时使用）
  const getRawUser = () => auth?.currentUser || null;

  return {
    currentUser,
    authInitialized,
    loading,
    error,
    loginWithGoogle,
    loginWithEmail,
    registerWithEmail,
    resetPassword,
    sendVerificationEmail,
    refreshEmailVerificationStatus,
    logout,
    getRawUser,
  };
};
