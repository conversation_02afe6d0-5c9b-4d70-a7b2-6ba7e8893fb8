import { ref, readonly } from 'vue'

export type ModalType = 'success' | 'info' | 'warning' | 'error'

interface NotificationOptions {
  type?: ModalType
  title?: string
  message: string
  buttonText?: string
  showCloseButton?: boolean
  allowBackdropClose?: boolean
}

interface NotificationState {
  visible: boolean
  type: ModalType
  title: string
  message: string
  buttonText: string
  showCloseButton: boolean
  allowBackdropClose: boolean
}

export const useNotificationModal = () => {
  const state = ref<NotificationState>({
    visible: false,
    type: 'success',
    title: '',
    message: '',
    buttonText: 'OK',
    showCloseButton: true,
    allowBackdropClose: true
  })

  const show = (options: NotificationOptions) => {
    state.value = {
      visible: true,
      type: options.type || 'success',
      title: options.title || '',
      message: options.message,
      buttonText: options.buttonText || 'OK',
      showCloseButton: options.showCloseButton !== false,
      allowBackdropClose: options.allowBackdropClose !== false
    }
  }

  const hide = () => {
    state.value.visible = false
  }

  // 便捷方法
  const showSuccess = (message: string, title?: string) => {
    show({
      type: 'success',
      title,
      message
    })
  }

  const showInfo = (message: string, title?: string) => {
    show({
      type: 'info',
      title,
      message
    })
  }

  const showWarning = (message: string, title?: string) => {
    show({
      type: 'warning',
      title,
      message
    })
  }

  const showError = (message: string, title?: string) => {
    show({
      type: 'error',
      title,
      message
    })
  }

  return {
    state: readonly(state),
    show,
    hide,
    showSuccess,
    showInfo,
    showWarning,
    showError
  }
}
