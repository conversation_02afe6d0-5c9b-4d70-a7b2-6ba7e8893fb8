<template>
  <div class="h-full app-background">
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </div>
</template>

<script setup lang="ts">
  import '~/assets/styles/clash/css/clash-display.css'
  import '~/assets/styles/global.css'
  import '~/assets/css/theme.css'

  // Initialize theme on app start - only execute on client side
  onMounted(() => {
    const savedTheme = localStorage.getItem('theme')
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    const theme = savedTheme || (prefersDark ? 'dark' : 'light')

    const html = document.documentElement
    if (theme === 'dark') {
      html.classList.add('dark')
      html.setAttribute('data-theme', 'dark')
      // document.body.classList.add('dark')
    } else {
      html.classList.remove('dark')
      html.setAttribute('data-theme', 'light')
      // document.body.classList.remove('dark')
    }
  })
</script>

<style>
  @import '@unocss/reset/tailwind-compat.css';
  @import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

  body,
  html,
  #__nuxt {
    font-family: Poppins, sans-serif;
  }

  body,
  html {
    height: 100%;
    margin: 0;
    padding: 0;
    min-height: 100vh;
  }

  /* App background - 避免 SSR hydration 不匹配 */
  .app-background {
    background-color: #F4F2F1;
    transition: background-color 0.3s ease;
  }

  /* Ensure dark mode styles are applied */
  .dark {
    color-scheme: dark;
  }

  .dark .app-background {
    background-color: #0F0F0F;
  }

  .dark body {
    background-color: #0F0F0F;
    color: #ffffff;
  }
</style>
