# Scholar分享功能优化测试文档

## 优化内容总结

### 1. 状态管理优化
- 新增 `ogImageBlob` 状态：缓存生成的图片Blob
- 新增 `ogImageGenerating` 状态：跟踪图片生成进度
- 优化 `ogImageGenerated` 状态：确保生成完成标记

### 2. OG图片生成优化
- 在生成OG图片时同时缓存Blob到本地
- 添加生成状态管理，避免重复生成
- 完善错误处理和状态清理

### 3. 下载功能重构
- **优先策略**：使用已缓存的OG图片Blob
- **降级策略**：实时截图生成（原有逻辑）
- **状态显示**：根据OG图片生成状态显示不同按钮文本

### 4. 用户体验提升
- 下载按钮状态：
  - `Preparing...`：OG图片生成中，按钮禁用
  - `Download`：OG图片已生成，可快速下载
  - `Download`：降级模式，实时截图下载
- 下载速度：使用缓存图片时几乎瞬时完成

## 测试场景

### 场景1：正常流程
1. 访问Scholar分析页面
2. 页面加载完成后自动开始生成OG图片
3. 下载按钮显示"Preparing..."状态
4. OG图片生成完成后，下载按钮变为可用
5. 点击下载按钮，瞬时下载缓存的图片

### 场景2：OG生成失败降级
1. 访问Scholar分析页面
2. OG图片生成过程中出现错误
3. 下载按钮恢复为正常状态
4. 点击下载按钮，使用实时截图功能

### 场景3：快速操作
1. 页面刚加载完成，OG图片还在生成中
2. 用户立即点击下载按钮
3. 按钮处于禁用状态，无法点击
4. 等待OG图片生成完成后才能下载

## 技术实现要点

### Props传递
```vue
<!-- Scholar页面传递给ShareCard -->
:og-image-blob="ogImageBlob"
:og-image-generating="ogImageGenerating" 
:og-image-generated="ogImageGenerated"
```

### 状态计算
```typescript
const downloadButtonState = computed(() => {
  if (isDownloading.value) return 'downloading'
  if (props.ogImageGenerating) return 'generating'
  if (props.ogImageGenerated && props.ogImageBlob) return 'ready'
  return 'fallback'
})
```

### 下载逻辑
```typescript
// 优先使用缓存的OG图片
if (props.ogImageGenerated && props.ogImageBlob) {
  await downloadFromBlob(props.ogImageBlob)
  return
}
// 降级到实时截图
await downloadFromScreenshot()
```

## 预期收益

1. **性能提升**：避免重复的html2canvas调用
2. **用户体验**：下载响应更快，状态提示更清晰
3. **资源节约**：减少客户端计算负担
4. **一致性**：下载图片与OG图片完全一致
5. **健壮性**：完善的降级机制和错误处理

## 兼容性说明

- 保留了原有的实时截图功能作为降级方案
- 对现有API和组件接口保持向后兼容
- 新增的props都是可选的，不影响其他页面使用ShareCard组件
