# API接口直连改造记录

## 📋 项目背景
- **目标**：将 recommend 和 graph 接口改成直连方式
- **原始配置**：开发环境使用代理，生产环境使用直连
- **遇到问题**：CORS 跨域问题和 Cloudflare Pages 代理限制

---

## 🛠️ 完整解决过程

### 第一步：尝试统一直连方式
**时间**：初始尝试  
**操作**：
- 修改 `api/recommend.ts` 和 `api/index.ts`
- 移除环境判断逻辑，统一使用 `https://search.dinq.io` 直连

**结果**：❌ 失败
- **错误**：CORS 跨域问题
- **原因**：浏览器阻止直接请求 `https://search.dinq.io`

### 第二步：恢复代理配置
**操作**：
- 恢复原有的环境判断逻辑
- 开发环境使用代理，生产环境使用直连

**结果**：❌ 仍有问题
- **错误**：403 Forbidden
- **原因**：协议不匹配（代理配置使用 HTTP，实际需要 HTTPS）

### 第三步：修复协议不匹配
**操作**：
- 修改 `nuxt.config.ts` 中的代理配置
- 将 `http://search.dinq.io` 改为 `https://search.dinq.io`

**结果**：✅ 部分成功
- recommend 接口：生产环境直连正常 ✅
- graph 接口：生产环境直连失败（CORS 问题）❌

### 第四步：采用保守方案
**决策**：保持 recommend 接口不变，只修改 graph 接口

**操作**：
- recommend 接口：保持开发代理 + 生产直连
- graph 接口：统一使用代理方式

### 第五步：尝试 Cloudflare Pages TOML 配置
**操作**：
- 在 `cloudflare-pages.toml` 中添加重定向规则
```toml
[[redirects]]
  from = "/api/v1/graph"
  to = "https://search.dinq.io/api/v1/graph"
  status = 200
  force = false
```

**结果**：❌ 失败
- **错误**：404 Not Found
- **问题**：请求仍发送到 `dinq.io` 而不是 `search.dinq.io`

### 第六步：尝试 _redirects 文件
**操作**：
- 创建 `public/_redirects` 文件
```
/api/v1/graph https://search.dinq.io/api/v1/graph 200
```

**结果**：❌ 失败
- **原因**：Cloudflare Pages 的 `_redirects` 不支持代理外部域名

### 第七步：深入调研 Cloudflare Pages 限制
**发现**：
- `_redirects` 文件：不支持代理外部域名
- `cloudflare-pages.toml`：重定向功能不可靠
- **核心限制**：Cloudflare Pages 原生不支持外部域名代理

### 第八步：最终解决方案 - Pages Functions
**操作**：
1. 删除无效的 `public/_redirects` 文件
2. 创建 `functions/api/v1/graph.js` Pages Function
3. 清理 `cloudflare-pages.toml` 中的无效重定向规则

**Pages Function 代码**：
```javascript
export async function onRequest(context) {
  const { request } = context;
  const url = new URL(request.url);
  
  // 构建目标 URL
  const targetUrl = `https://search.dinq.io/api/v1/graph${url.search}`;
  
  // 转发请求
  const response = await fetch(targetUrl, {
    method: request.method,
    headers: request.headers,
    body: request.body
  });
  
  return response;
}
```

---

## ✅ 最终配置状态

### Recommend 接口
- **开发环境**：`/api/v1/recommend` → Nuxt 代理 → `https://search.dinq.io/api/v1/recommend`
- **生产环境**：直连 `https://search.dinq.io/api/v1/recommend`

### Graph 接口
- **开发环境**：`/api/v1/graph` → Nuxt 代理 → `https://search.dinq.io/api/v1/graph`
- **生产环境**：`/api/v1/graph` → Pages Function → `https://search.dinq.io/api/v1/graph`

---

## 🎯 关键经验总结

### 1. Cloudflare Pages 的限制
- `_redirects` 文件不支持代理外部域名
- `cloudflare-pages.toml` 的重定向功能不够可靠
- Pages Functions 是代理外部 API 的最佳方案

### 2. CORS 问题的根本原因
- 不同接口的 CORS 配置不同
- recommend 接口支持跨域访问
- graph 接口不支持跨域访问

### 3. 保守方案的重要性
- 保持已正常工作的功能不变
- 只修改有问题的部分
- 降低风险，提高成功率

### 4. 调试技巧
- 通过浏览器开发者工具查看实际请求 URL
- 分析 HTTP 状态码和错误信息
- 联网搜索官方文档和最佳实践

---

## 📁 涉及的文件清单

### 修改的文件
- `api/index.ts` - graph 接口统一使用代理
- `nuxt.config.ts` - 修复代理协议（HTTP → HTTPS）
- `cloudflare-pages.toml` - 清理无效重定向规则

### 新增的文件
- `functions/api/v1/graph.js` - Pages Function 代理

### 删除的文件
- `public/_redirects` - 无效的重定向配置

### 保持不变的文件
- `api/recommend.ts` - 保持原有配置（开发代理 + 生产直连）

---

## 🚀 部署后验证清单

- [ ] recommend 接口在开发环境正常
- [ ] recommend 接口在生产环境正常
- [ ] graph 接口在开发环境正常
- [ ] graph 接口在生产环境正常（通过 Pages Function）
- [ ] 其他 API 接口不受影响

---

**记录时间**：2025年6月20日  
**问题状态**：✅ 已解决  
**解决方案**：Cloudflare Pages Functions 代理 