# DINQ 前端 - 动态 OG 图片功能预发布测试方案

## 🎯 测试目标

验证 nuxt-og-image 模块在 Cloudflare Pages 生产环境的表现，确保动态 OG 图片功能正常工作后再发布到正式环境。

## 🌿 1. 预发布环境方案

### 方案 A: Cloudflare Pages 预览部署（推荐）

**优势**: 
- 无需额外配置，Cloudflare Pages 原生支持
- 每个 PR 和分支推送自动生成预览链接
- 与生产环境完全相同的运行时环境

**实施步骤**:

#### 1.1 创建测试分支
```bash
# 从当前开发分支创建测试分支
git checkout -b feature/og-image-testing
git push origin feature/og-image-testing
```

#### 1.2 Cloudflare Pages 自动预览
- Cloudflare Pages 会自动为新分支创建预览部署
- 预览 URL 格式: `https://[commit-hash].dinq-frontend.pages.dev`
- 每次推送都会生成新的预览链接

### 方案 B: 独立的 Staging 环境

**适用场景**: 需要稳定的测试环境 URL

#### 1.1 创建 Staging 项目
在 Cloudflare Pages 控制台:
1. 创建新项目 `dinq-frontend-staging`
2. 连接到同一个 Git 仓库
3. 设置部署分支为 `staging`

#### 1.2 配置独立域名
```
生产环境: dinq.io
测试环境: staging.dinq.io 或 test-dinq.pages.dev
```

## 🔀 2. 分支策略建议

### 推荐的 Git Flow

```
main (稳定版本)
├── nuxt (生产部署分支)
├── staging (预发布测试分支)
└── feature/og-image-testing (功能开发分支)
```

### 分支管理流程

#### 2.1 创建 Staging 分支
```bash
# 从 nuxt 分支创建 staging 分支
git checkout nuxt
git pull origin nuxt
git checkout -b staging
git push origin staging
```

#### 2.2 功能测试流程
```bash
# 1. 在功能分支开发
git checkout -b feature/og-image-testing

# 2. 完成开发后合并到 staging
git checkout staging
git merge feature/og-image-testing
git push origin staging

# 3. 测试通过后合并到 nuxt (生产)
git checkout nuxt
git merge staging
git push origin nuxt
```

## ⚙️ 3. Cloudflare Pages 配置

### 3.1 多环境部署配置

#### 生产环境 (现有)
```yaml
项目名称: dinq-frontend
部署分支: nuxt
域名: dinq.io
环境: Production
```

#### 测试环境 (新建)
```yaml
项目名称: dinq-frontend-staging
部署分支: staging
域名: staging.dinq.io
环境: Preview
```

### 3.2 环境变量分离

#### 生产环境变量
```bash
# Cloudflare Pages > dinq-frontend > Settings > Environment variables
NUXT_PUBLIC_SITE_URL=https://dinq.io
NUXT_OG_IMAGE_ENABLED=true
NITRO_PRESET=cloudflare-pages
NODE_ENV=production
```

#### 测试环境变量
```bash
# Cloudflare Pages > dinq-frontend-staging > Settings > Environment variables
NUXT_PUBLIC_SITE_URL=https://staging.dinq.io
NUXT_OG_IMAGE_ENABLED=true
NITRO_PRESET=cloudflare-pages
NODE_ENV=staging
DEBUG_OG_IMAGE=true  # 额外的调试标志
```

### 3.3 构建配置

#### 两个环境使用相同的构建设置
```yaml
构建命令: npm run build
输出目录: .output/public
Node.js 版本: 18.x
环境变量: 如上所述
```

### 3.4 自定义域名设置（可选）

#### 为测试环境配置子域名
1. **Cloudflare DNS 设置**:
   ```
   类型: CNAME
   名称: staging
   目标: dinq-frontend-staging.pages.dev
   ```

2. **Cloudflare Pages 域名配置**:
   - 进入 `dinq-frontend-staging` 项目
   - Custom domains > Add custom domain
   - 添加 `staging.dinq.io`

## 🧪 4. 测试验证步骤

### 4.1 部署后基础验证

#### 步骤 1: 检查部署状态
```bash
# 访问测试环境
https://staging.dinq.io

# 检查页面是否正常加载
curl -I https://staging.dinq.io/report
```

#### 步骤 2: 验证 nuxt-og-image 模块

```bash
# 测试 OG 图片端点
curl -I "https://staging.dinq.io/__og-image__/image.png"

# 预期结果: HTTP/1.1 200 OK (而不是 404)
```

#### 步骤 3: 检查 Cloudflare Pages Functions
1. 进入 Cloudflare Pages 控制台
2. 选择 `dinq-frontend-staging` 项目
3. 查看 **Functions** 标签页
4. 确认是否有 OG 相关的 Edge Functions

### 4.2 OG 图片功能深度测试

#### 测试 1: 直接访问 OG 图片
```bash
# 测试带参数的 OG 图片生成
curl "https://staging.dinq.io/__og-image__/image.png?component=OgImageTemplate"

# 检查返回的 Content-Type
# 预期: image/png 或 image/svg+xml
```

#### 测试 2: 报告页面 Meta 标签验证
```bash
# 获取页面 HTML
curl "https://staging.dinq.io/report" | grep -i "og:image"

# 预期输出包含:
# <meta property="og:image" content="https://staging.dinq.io/__og-image__/...">
```

#### 测试 3: 社交媒体平台验证

**Facebook Sharing Debugger**:
1. 访问: https://developers.facebook.com/tools/debug/
2. 输入: `https://staging.dinq.io/report`
3. 点击 "Debug"
4. 检查是否正确显示动态 OG 图片

**Twitter Card Validator**:
1. 访问: https://cards-dev.twitter.com/validator
2. 输入: `https://staging.dinq.io/report`
3. 点击 "Preview card"
4. 验证 Twitter Card 显示效果

**LinkedIn Post Inspector**:
1. 访问: https://www.linkedin.com/post-inspector/
2. 输入测试 URL
3. 检查预览效果

### 4.3 性能和功能测试

#### 测试 4: 动态数据验证
```javascript
// 在浏览器控制台执行
// 检查 OG 图片 URL 是否包含动态参数
const ogImage = document.querySelector('meta[property="og:image"]');
console.log('OG Image URL:', ogImage?.content);

// 验证 URL 是否包含用户特定数据
// 例如: /__og-image__/image.png?name=...&papers=...
```

#### 测试 5: 缓存和性能
```bash
# 多次请求同一个 OG 图片，检查缓存头
curl -I "https://staging.dinq.io/__og-image__/image.png" -H "Cache-Control: no-cache"

# 检查响应时间
time curl "https://staging.dinq.io/__og-image__/image.png" > /dev/null
```

## 🔄 5. 回滚策略

### 5.1 快速回滚方案

#### 方案 A: Git 回滚
```bash
# 如果问题在 staging 分支
git checkout staging
git reset --hard HEAD~1  # 回滚到上一个提交
git push origin staging --force

# 如果问题已经合并到 nuxt 分支
git checkout nuxt
git revert <commit-hash>  # 创建回滚提交
git push origin nuxt
```

#### 方案 B: Cloudflare Pages 回滚
1. 进入 Cloudflare Pages 控制台
2. 选择项目 > Deployments
3. 找到上一个稳定的部署
4. 点击 "Rollback to this deployment"

### 5.2 紧急回滚预案

#### 准备工作
```bash
# 在测试前创建稳定版本标签
git tag -a v1.0-stable -m "Stable version before OG image feature"
git push origin v1.0-stable
```

#### 紧急回滚
```bash
# 快速回滚到稳定版本
git checkout nuxt
git reset --hard v1.0-stable
git push origin nuxt --force
```

## 📋 6. 测试检查清单

### 部署前检查
- [ ] 创建 staging 分支
- [ ] 配置 Cloudflare Pages staging 环境
- [ ] 设置环境变量
- [ ] 推送代码到 staging 分支

### 功能测试检查
- [ ] `/__og-image__` 端点返回 200 状态
- [ ] OG 图片正确生成（非 404）
- [ ] Meta 标签包含正确的 OG 图片 URL
- [ ] Facebook Sharing Debugger 验证通过
- [ ] Twitter Card Validator 验证通过
- [ ] 动态数据正确显示在 OG 图片中

### 性能测试检查
- [ ] OG 图片生成时间 < 3 秒
- [ ] 图片大小合理（< 1MB）
- [ ] 缓存策略正常工作
- [ ] Edge Functions 执行无错误

### 生产发布检查
- [ ] 所有测试通过
- [ ] 代码审查完成
- [ ] 合并到 nuxt 分支
- [ ] 监控生产环境部署

## 🚀 7. 实施时间表

### 第 1 天: 环境准备
- 创建 staging 分支和 Cloudflare Pages 项目
- 配置环境变量和域名
- 部署初始版本

### 第 2 天: 功能测试
- 验证 nuxt-og-image 模块工作状态
- 测试 OG 图片生成功能
- 社交媒体平台验证

### 第 3 天: 优化和发布
- 根据测试结果进行调优
- 完成最终验证
- 合并到生产分支

---

这个方案确保了在不影响生产环境的情况下，充分测试 OG 图片功能的各个方面。特别关注了 nuxt-og-image 模块在 Cloudflare Pages 环境的表现，这是当前最关键的验证点。
