# Cloudflare Pages 环境变量配置

## 问题说明

当代码推送到生产分支时，API 请求从 `https://api.dinq.io` 变成了 `https://dinq.io`，这是因为 Cloudflare Pages 的环境变量配置不完整。

## 必需的环境变量

在 Cloudflare Pages 的控制台中，需要为**生产分支**设置以下环境变量：

### 1. API 配置
```
NUXT_PUBLIC_API_BASE=https://api.dinq.io
```

### 2. 站点 URL 配置（关键！）
```
NUXT_PUBLIC_SITE_URL=https://dinq.io
```

## 配置步骤

1. 登录 Cloudflare Pages 控制台
2. 进入你的项目设置
3. 找到 "Environment variables" 部分
4. 为 **Production** 环境添加上述环境变量
5. 重新部署项目

## 验证配置

部署后，在浏览器控制台中运行：
```javascript
// 检查配置是否正确
console.log('API Base:', useRuntimeConfig().public.apiBase)
console.log('Site URL:', useRuntimeConfig().public.siteUrl)
```

## 常见问题

### Q: 为什么普通分支正常，生产分支有问题？
A: 普通分支可能使用默认环境变量，而生产分支需要显式配置。

### Q: 如何确认环境变量生效？
A: 重新部署后，检查 Network 面板中的 API 请求 URL 是否正确。

## 构建脚本支持

现在 `build.sh` 支持传入自定义域名：
```bash
# 使用默认域名
./build.sh

# 使用自定义 API 域名
./build.sh https://custom-api.example.com

# 使用自定义 API 和站点域名
./build.sh https://custom-api.example.com https://custom-site.example.com
```