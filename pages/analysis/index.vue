<template>
  <div class="mt-15 px-30 page-fade-in">
    <div class="text-center clash-semibold text-12">
      <div class="analysis-title mb-1">
        Analysis of Al Researcher
      </div>
      <div class="text-primary-100 text-56px h-[73px] mb-6">
        <TypedText
          :strings="[
            'Know Your AI Peers',
            'LLM',
            'Text-to-Image',
            'Text-to-Video',
            'Text-to-3D',
            'RAG',
            'Reinforcement Learning',
            'Vision Language Model',
            'Security & Safety',
            'Agent',
            'Information Retrieval',
          ]"
          :typeSpeed="100"
          :backSpeed="30"
          :loop="true"
        />
      </div>
    </div>
    <div class="new-toggle-container ma mb-10">
      <!-- Background container -->
      <div class="new-toggle-background">
        <!-- Slider indicator -->
        <div
          class="new-toggle-slider"
          :class="{ 'slide-to-github': selected === 'github' }"
        ></div>

        <!-- 选项按钮 -->
        <div
          class="new-toggle-option"
          :class="{ 'active': selected === 'scholar' }"
          @click="selectOption('scholar')"
        >
          Scholar
        </div>
        <div
          class="new-toggle-option"
          :class="{ 'active': selected === 'github' }"
          @click="selectOption('github')"
        >
          Github
        </div>
      </div>
    </div>
    
    <div class="mt-4">
      <SearchBar :type="selected" />
      <div class="text-center mt-2 terms-text">
        By clicking "Analyze", you agree to our
        <a
          href="/terms"
          class="text-primary-100 hover:underline"
          target="_blank"
          rel="noopener noreferrer"
          >Terms of Service</a
        >.
      </div>
    </div>

    <div class="mt-6 min-h-92px">
      <div class="cards-container f-cer gap-8 mb-8 min-h-23">
        <!-- Scholar Cards -->
        <div
          v-if="selected === 'scholar'"
          v-for="(item, index) in data"
          :key="`scholar-${refreshKey}-${index}`"
          class="card-item"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <PeopleCard
            :people="item"
            :index="index"
            @search="handleSearchPeople"
            @compare="handleComparePerson(item)"
          />
        </div>
        
        <!-- GitHub Cards -->
        <div
          v-if="selected === 'github'"
          v-for="(item, index) in githubData"
          :key="`github-${refreshKey}-${index}`"
          class="card-item"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <GitHubCard
            :developer="item"
            :index="index"
            @search="handleSearchGitHubDeveloper"
            @compare="handleCompareGitHubDeveloper(item)"
          />
        </div>
      </div>
      <div class="f-cer">
        <div
          class="wh-10 cursor-pointer hover:scale-105 transition-transform refresh-btn"
          :class="{ spin: isSpinning }"
          @click="handleRefresh"
        />
      </div>
    </div>

    <div class="unlock-bg">
      <div
        class="unlock-title text-center flex flex-col items-center justify-center mx-auto"
        style="line-height: 1.3"
      >
        <span>Unlock AI pioneers</span>
        <span>for your firm</span>
      </div>
      <div class="unlock-description mt-4 mb-6">
        Top AI devs for Big tech, AI startup, solution service providers and academia.
      </div>
      <button
        class="rounded-full bg-black-100 py-4 px-7 font-medium text-sm text-white hover:bg-black-100/90 hover:scale-102 transition-all dark:bg-white dark:text-black dark:hover:bg-gray-100 dark:hover:scale-102"
        @click="router.push('/demo')"
      >
        Request a Demo
      </button>
    </div>

    <FaqAccordion />

    <InviteCodeModal
      v-if="showInviteModal"
      @close="showInviteModal = false"
      @waiting-list="showInviteModal = false"
      @submit="showInviteModal = false"
    />

    <CompareModal
      v-if="showCompareModal && comparePerson && !isGitHubComparison"
      :person="comparePerson as Record<string, any>"
      @close="handleCloseCompare"
      @compare="handleDoCompare"
    />

    <GitHubCompareModal
      v-if="showCompareModal && comparePerson && isGitHubComparison"
      :developer="comparePerson as GitHubDeveloper"
      @close="handleCloseCompare"
      @compare="handleDoGitHubCompare"
    />
  </div>
</template>

<script setup lang="ts">
  import { getTalentsData, getGitHubDevPioneers } from '~/api'
  import type { Talent } from '~/api/types'
  import type { GitHubDeveloper } from '~/api'
  import InviteCodeModal from '~/components/InviteCodeModal.vue'
  import CompareModal from '~/components/CompareModal.vue'
  import GitHubCompareModal from '~/components/GitHubCompareModal.vue'
  import GitHubCard from '~/components/GitHubCard/index.vue'
  import { extractGitHubUsername } from '~/utils'
  import { onMounted } from 'vue'

  const { currentUser } = useFirebaseAuth()
  const { $emitter } = useNuxtApp()
  const router = useRouter()
  const isSpinning = ref(false)
  const refreshKey = ref(0)
  const isRefreshing = ref(false)
  const data = ref<Talent[]>([])
  const githubData = ref<GitHubDeveloper[]>([])

  // mock: 用户是否有激活码
  const hasInviteCode = ref(false)
  const showInviteModal = ref(false)

  const showCompareModal = ref(false)
  const comparePerson = ref<Talent | GitHubDeveloper | null>(null)
  const isGitHubComparison = ref(false)
  const selected = ref('scholar') // 默认选中 Scholar

  // 切换选项的方法
  const selectOption = (option: string) => {
    selected.value = option
    // 数据已在页面初始化时加载，无需再次获取
  }



  const handleRefresh = async () => {
    if (isRefreshing.value) return

    try {
      isRefreshing.value = true
      isSpinning.value = true
      if (selected.value === 'scholar') {
        await handleFetchTalents()
      } else if (selected.value === 'github') {
        await handleFetchGitHubDevelopers()
      }
    } finally {
      isRefreshing.value = false
      isSpinning.value = false
    }
  }

  const handleSearchPeople = ({ type, identifier }: { type: 'scholar' | 'name', identifier: string }) => {
    if (!currentUser.value) {
      $emitter.emit('auth')
      return
    }

    if (type === 'scholar') {
      // 有 Scholar ID，直接跳转到 Scholar 页面
      console.log('Analysis page handleSearchPeople: Direct to Scholar page', {
        type,
        identifier,
        navigating_to: `/scholar?user=${identifier}`
      })
      router.push(`/scholar?user=${identifier}`)
    } else {
      // 只有 name，需要通过 Report 过境页分析
      console.log('Analysis page handleSearchPeople: Via Report transit page', {
        type,
        identifier,
        navigating_to: `/report?query=${identifier}`
      })
      router.push(`/report?query=${identifier}`)
    }
  }

  const handleFetchTalents = async () => {
    try {
      isSpinning.value = true
      const res = await getTalentsData(3)
      console.log('handleFetchTalents', res)
      if (res.data.success) {
        data.value = res.data.talents
      }
    } catch (error) {
      console.error('Error fetching talents:', error)
    } finally {
      refreshKey.value++
      isSpinning.value = false
    }
  }

  const handleFetchGitHubDevelopers = async () => {
    try {
      isSpinning.value = true
      const res = await getGitHubDevPioneers(3, {})
      console.log('handleFetchGitHubDevelopers', res)
      if (res.data?.success) {
        // 只显示有GitHub链接的开发者
        githubData.value = res.data.pioneers.filter(dev => dev.has_github)
      }
    } catch (error) {
      console.error('Error fetching GitHub developers:', error)
    } finally {
      refreshKey.value++
      isSpinning.value = false
    }
  }

  const handleSearchGitHubDeveloper = (developer: string) => {
    if (!currentUser.value) {
      $emitter.emit('auth')
      return
    }
    // Extract GitHub username from URL or use as-is if it's already a username
    const username = extractGitHubUsername(developer)
    router.push(`/github?user=${username}`)
  }

  const handleCompareGitHubDeveloper = (developer: GitHubDeveloper) => {
    if (!currentUser.value) {
      $emitter.emit('auth')
      return
    }
    comparePerson.value = developer
    isGitHubComparison.value = true
    showCompareModal.value = true
  }

  function handleComparePerson(person: Talent) {
    if (!currentUser.value) {
      $emitter.emit('auth')
      return
    }
    console.log('Analysis page handleComparePerson:', {
      person: person.name,
      has_scholar_id: !!person.google_scholar
    })
    comparePerson.value = person
    isGitHubComparison.value = false
    showCompareModal.value = true
  }

  function handleCloseCompare() {
    showCompareModal.value = false
    comparePerson.value = null
    isGitHubComparison.value = false
  }

  function handleDoCompare({ left, right }: { left: any; right: any }) {
    showCompareModal.value = false
    comparePerson.value = null
    isGitHubComparison.value = false
    if (left && right) {
      router.push(
        `/compare?researcher1=${encodeURIComponent(left.google_scholar)}&researcher2=${encodeURIComponent(right)}`
      )
    }
  }

  function handleDoGitHubCompare({ left, right }: { left: GitHubDeveloper; right: string }) {
    showCompareModal.value = false
    comparePerson.value = null
    isGitHubComparison.value = false
    if (left && right) {
      // Extract GitHub username from the left developer
      const leftUsername = extractGitHubUsername(left.github)
      const rightUsername = extractGitHubUsername(right)
      router.push(
        `/github_compare?user1=${encodeURIComponent(leftUsername)}&user2=${encodeURIComponent(rightUsername)}`
      )
    }
  }

  // 页面加载时同时获取学者和GitHub开发者数据，不需要等待用户认证
  onMounted(() => {
    console.log('Page mounted, fetching both talents and GitHub developers...')
    handleFetchTalents()
    handleFetchGitHubDevelopers()
  })
</script>

<style scoped>
  .page-fade-in {
    opacity: 0;
    animation: fadeIn 0.6s ease-out forwards;
  }

  @keyframes fadeIn {
    to {
      opacity: 1;
    }
  }

  /* New improved toggle styles */
  .new-toggle-container {
    position: relative;
    width: 220px;
    height: 48px;
    margin: 0 auto;
  }

  .new-toggle-background {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #EBD2C9;
    border-radius: 24px;
    padding: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .new-toggle-slider {
    position: absolute;
    top: 4px;
    left: 4px;
    width: calc(50% - 6px);
    height: calc(100% - 8px);
    background-color: #CB7C5D;
    border-radius: 20px;
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 1;
  }

  .new-toggle-slider.slide-to-github {
    transform: translateX(calc(100% + 4px));
  }

  .new-toggle-option {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    user-select: none;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    font-size: 14px;
    color: #BA6949;
    border-radius: 20px;
    transition: color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    z-index: 2;
  }

  .new-toggle-option:hover:not(.active) {
    color: #A55A3D;
  }

  .new-toggle-option.active {
    color: #FFFFFF;
  }

  /* Dark mode styles */
  .dark .new-toggle-background {
    background-color: #323232;
    border: 1px solid #3E3E3E;
  }

  .dark .new-toggle-slider {
    background-color: #CB7C5D;
  }

  .dark .new-toggle-option {
    color: #C6C6C6;
  }

  .dark .new-toggle-option:hover:not(.active) {
    color: #E0E0E0;
  }

  .dark .new-toggle-option.active {
    color: #FFFFFF;
  }

  .unlock-bg {
    background-image: url('/image/requestdemobg.png');
    background-size: 100%;
    margin-top: 30px;
    border-radius: 20px;
    @apply h-[315px] bg-no-repeat cover flex flex-col items-center justify-center;
  }

  /* 暗黑模式下的背景图片 */
  .dark .unlock-bg {
    background-image: url('~/assets/image/requestdemobg-dark.png');
  }

  .refresh-btn {
    background-image: url('~/assets/image/refresh-btn.png');
    background-size: 100%;
  }
  .dark .refresh-btn {
    background-image: url('~/assets/image/refresh-btn-gray.png');
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .spin {
    animation: spin 1s linear infinite;
  }

  .terms-text {
    font-family: Aeonik;
    font-weight: 400;
    font-size: 14px;
    line-height: 160%;
    letter-spacing: 0%;
    color: rgba(67, 74, 79, 0.6);
  }

  .dark .terms-text {
    color: rgba(201, 201, 201, 0.6);
  }

  .analysis-title {
    font-weight: 600;
    font-size: 56px;
    line-height: 130%;
    letter-spacing: 0%;
    text-align: center;
    color: #25324B;
  }

  .dark .analysis-title {
    color: #FFFFFF;
  }

  .text-56px {
    font-size: 56px;
    line-height: 130%;
    letter-spacing: 0%;
  }

  .unlock-title {
    font-family: 'ClashDisplay-Semibold', sans-serif;
    font-weight: 600;
    font-size: 42px;
    line-height: 130%;
    letter-spacing: 0%;
    color: #3D342D;
  }

  .dark .unlock-title {
    color: #FAF9F5;
  }

  .unlock-description {
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 160%;
    letter-spacing: 0%;
    color: #6B625D;
  }

  .dark .unlock-description {
    color: #888888;
  }

  /* 卡片动画 */
  .cards-container {
    position: relative;
  }

  .card-item {
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.4s ease-out forwards;
  }

  @keyframes slideInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 响应式优化 */
  @media (max-width: 640px) {
    .unlock-bg {
      height: 240px;
      background-size: 110%;
    }

    .new-toggle-container {
      width: 200px;
      height: 44px;
    }

    .new-toggle-background {
      border-radius: 22px;
      padding: 3px;
      gap: 3px;
    }

    .new-toggle-slider {
      top: 3px;
      left: 3px;
      width: 38px;
      height: 38px;
      border-radius: 50%;
    }

    .new-toggle-slider.slide-to-github {
      transform: translateX(156px);
    }

    .new-toggle-option {
      font-size: 13px;
      border-radius: 19px;
    }
  }
</style>
