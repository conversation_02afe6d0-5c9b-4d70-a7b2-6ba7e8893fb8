<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">
        Meta 标签调试页面
      </h1>
      
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">当前页面 Meta 标签</h2>
        <div class="space-y-2 font-mono text-sm">
          <div v-for="meta in currentMetas" :key="meta.name || meta.property" class="p-2 bg-gray-100 dark:bg-gray-700 rounded">
            <span class="text-blue-600 dark:text-blue-400">{{ meta.tag }}</span>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">测试 GitHub 页面 Meta 标签</h2>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium mb-2">GitHub 用户名:</label>
            <input 
              v-model="testUsername" 
              type="text" 
              placeholder="例如: octocat"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
          </div>
          <button 
            @click="testGitHubMeta"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            生成 GitHub 页面 Meta 标签
          </button>
        </div>
      </div>

      <div v-if="githubMetas.length > 0" class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">GitHub 页面 Meta 标签预览</h2>
        <div class="space-y-2 font-mono text-sm">
          <div v-for="meta in githubMetas" :key="meta" class="p-2 bg-gray-100 dark:bg-gray-700 rounded">
            {{ meta }}
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-semibold mb-4">验证工具</h2>
        <div class="space-y-4">
          <div>
            <h3 class="font-medium mb-2">Twitter Card Validator</h3>
            <a 
              href="https://cards-dev.twitter.com/validator" 
              target="_blank"
              class="text-blue-600 hover:text-blue-800 underline"
            >
              https://cards-dev.twitter.com/validator
            </a>
          </div>
          <div>
            <h3 class="font-medium mb-2">Facebook Sharing Debugger</h3>
            <a 
              href="https://developers.facebook.com/tools/debug/" 
              target="_blank"
              class="text-blue-600 hover:text-blue-800 underline"
            >
              https://developers.facebook.com/tools/debug/
            </a>
          </div>
          <div>
            <h3 class="font-medium mb-2">LinkedIn Post Inspector</h3>
            <a 
              href="https://www.linkedin.com/post-inspector/" 
              target="_blank"
              class="text-blue-600 hover:text-blue-800 underline"
            >
              https://www.linkedin.com/post-inspector/
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const testUsername = ref('octocat')
const currentMetas = ref<any[]>([])
const githubMetas = ref<string[]>([])

// 获取当前页面的 meta 标签
onMounted(() => {
  if (process.client) {
    const metas = document.querySelectorAll('meta')
    currentMetas.value = Array.from(metas).map(meta => {
      const name = meta.getAttribute('name')
      const property = meta.getAttribute('property')
      const content = meta.getAttribute('content')
      
      if (name) {
        return { tag: `<meta name="${name}" content="${content}" />`, name, content }
      } else if (property) {
        return { tag: `<meta property="${property}" content="${content}" />`, property, content }
      }
      return null
    }).filter(Boolean)
  }
})

// 生成 GitHub 页面的 meta 标签
const testGitHubMeta = () => {
  if (!testUsername.value) return
  
  const username = testUsername.value
  const title = `${username} - GitHub Developer Profile | DINQ`
  const description = `View ${username}'s GitHub developer profile, code contributions, repositories, and programming skills analysis on DINQ.`
  const url = `https://dinq.io/github?user=${username}`
  const image = `https://github.com/${username}.png`
  
  githubMetas.value = [
    `<title>${title}</title>`,
    `<meta name="description" content="${description}" />`,
    `<meta name="keywords" content="${username}, GitHub Developer, Software Engineer, Developer Profile" />`,
    `<meta property="og:title" content="${title}" />`,
    `<meta property="og:description" content="${description}" />`,
    `<meta property="og:type" content="profile" />`,
    `<meta property="og:url" content="${url}" />`,
    `<meta property="og:image" content="${image}" />`,
    `<meta name="twitter:card" content="summary_large_image" />`,
    `<meta name="twitter:title" content="${title}" />`,
    `<meta name="twitter:description" content="${description}" />`,
    `<meta name="twitter:image" content="${image}" />`,
    `<meta property="profile:username" content="${username}" />`,
    `<link rel="canonical" href="${url}" />`
  ]
}

// 设置页面 meta
useSeoMeta({
  title: 'Meta 标签调试页面 | DINQ',
  description: '调试和验证 DINQ 页面的 meta 标签生成',
  robots: 'noindex, nofollow' // 防止搜索引擎索引调试页面
})
</script>
