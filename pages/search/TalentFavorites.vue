<template>
  <div
    class="min-h-screen p-4 transition-colors duration-300 dark:text-white text-gray-900"
    :class="{ 'pt-15': isMobile }"
  >
    <div class="talent-title">
      <div class="flex items-center mb-4 h-10">
        <button
          v-if="currentFolder"
          @click="exitFolder"
          class="mr-2 text-gray-500 hover:text-black dark:hover:text-white bg-transparent"
        >
          <img src="~/assets/image/Left.svg" alt="" class="w-6 h-6" />
        </button>

        <!-- talents title -->
        <template v-if="currentFolder">
          <input
            v-if="currentFolder.editing"
            v-model="currentFolder.name"
            @blur="saveFolderName(currentFolder)"
            class="text-xl font-semibold bg-white dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-300 dark:border-gray-600 rounded px-2 py-1 w-64"
          />
          <h2
            v-else
            class="text-xl font-semibold flex-1 cursor-pointer hover:underline"
            @click="editFolderName(currentFolder)"
          >
            {{ currentFolder.name || 'Unnamed Folder' }}
          </h2>
        </template>

        <h2 v-else class="text-xl font-semibold flex-1">Talents</h2>
      </div>

      <div class="fx-cer gap-4">
        <!-- New Folder -->
        <button
          v-if="!currentFolder"
          @click="addFolder"
          class="text-3 fx-cer w-[120px] h-[32px] gap-2 border border-black/50 rounded-1 px-3 py-1 rounded bg-transparent text-black dark:( text-gray-400 border-gray-400)"
        >
          <img src="~/assets/image/add-folder 1.svg" alt="" class="btn-icon btn-icon-light" />
          <img src="~/assets/image/add-folder 2.svg" alt="" class="btn-icon btn-icon-dark" />
          New Folder
        </button>
        <!-- upload -->
        <button
          v-if="!currentFolder"
          @click="props.onGoToUpload"
          class="text-3 fx-cer justify-center w-[120px] h-[32px] gap-2 border border-black/50 rounded-1 px-3 py-1 rounded bg-transparent text-black dark:( text-gray-400 border-gray-400)"
        >
          <img src="~/assets/image/upload1.svg" alt="" class="btn-icon btn-icon-light" />
          <img src="~/assets/image/upload2.svg" alt="" class="btn-icon btn-icon-dark" />
          Upload
        </button>
      </div>
    </div>

    <!-- Folders and cards -->
    <DragFolderCard v-model="mainList" class="mt-4">
      <template #default="{ item }">
        <div
          v-if="item.type === 'folder'"
          draggable="true"
          class="relative folder-box rounded-lg p-5 cursor-pointer h-[215px] transition-all duration-150 hover:shadow-md text-gray-900 dark:(text-white)"
        >
          <!-- Folder菜单 -->
          <div class="absolute top-6 right-2 z-20">
            <button
              @click.stop="toggleMenu(item)"
              class="w-6 h-6 text-gray-500 hover:text-black dark:hover:text-white dark:text-gray-400 fx-cer justify-center bg-transparent"
            >
              <img src="~/assets/image/More.svg" alt="" />
            </button>
            <div
              v-if="item.showMenu"
              class="absolute right-0 mt-1 rounded shadow z-30 w-24 px-0 py-1 bg-white border border-gray-300 dark:(bg-gray-700 border-gray-600)"
            >
              <div
                @click="confirmDelete(item)"
                class="cursor-pointer fx-cer gap-2 px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-800 dark:text-white"
              >
                <img src="~/assets/image/Delete.svg" alt="" />
                <span>Delete</span>
              </div>
            </div>
          </div>
          <!-- 文件夹内容缩略图 -->
          <div @click="enterFolder(item)" class="cursor-pointer mb-2 h-[120px] relative z-10 mt-5">
            <div
              class="grid grid-cols-3 grid-rows-2 gap-1 justify-center overflow-hidden p-2 text-center"
              style="width: 100%; margin: 0 auto"
            >
              <div
                v-for="(user, i) in item.contents.slice(0, 6)"
                :key="i"
                class="flex items-center justify-center"
              >
                <img
                  :src="user.avatar"
                  alt="avatar"
                  class="w-12 h-12 rounded-full border border-white dark:border-gray-800"
                  :title="user.name"
                />
              </div>
            </div>
          </div>

          <!-- 文件夹名（在外部已处理） -->
          <div class="mt-1 relative z-10 text-left">
            <span
              v-if="!item.editing"
              @click.stop="editFolderName(item)"
              class="cursor-pointer hover:underline text-sm"
            >
              {{ item.name || 'Unnamed Folder' }}
            </span>
            <input
              v-else
              v-model="item.name"
              @blur="saveFolderName(item)"
              class="text-sm border rounded w-full px-2 py-1 bg-white text-gray-900 border-gray-300 dark:(bg-gray-700 text-white border-gray-500)"
            />
          </div>
        </div>

        <div
          v-else
          class="relative fx-cer flex-col gap-5 rounded-lg h-[215px] p-4 cursor-move transition-all duration-150 hover:shadow-md hover:border-orange-500 border bg-white text-gray-900 dark:(bg-[#242425] border-gray-600 text-white)"
          draggable="true"
          @click="openCardModal(item)"
        >
          <!-- favorites -->
          <button
            class="absolute right-2 top-2 w-8 h-8 rounded-2 fx-cer justify-center bg-transparent dark:border-[#323232]"
            @click.stop="toggleFavorite(item)"
          >
            <img
              :src="item.isFavorited ? starFill2 : starEmpty2"
              alt="favorites"
              class="btn-icon btn-icon-dark"
            />
            <img
              :src="item.isFavorited ? starFill : starEmpty"
              alt="favorites"
              class="btn-icon btn-icon-light"
            />
          </button>
          <div class="flex items-center gap-2 justify-start w-full">
            <img :src="item.avatar" alt="avatar" class="w-12 h-12 rounded-full" />
            <div class="text-left text-sm">
              <p class="text-4 font-600">{{ item.name }}</p>
              <p class="fx-cer gap-1">
                <img src="~/assets/svg/verified.svg" alt="" />
                <span class="text-3 font-400 font-[#666]"
                  >{{ item.title }} , {{ item.company }}</span
                >
              </p>
            </div>
          </div>
          <div class="text-[16px] text-[#3c3c3c] dark:text-gray-300">
            <span v-for="(tag, i) in item.tags" :key="i" class="mx-1">{{ tag }}</span> |
            {{ item.title }} |
            {{ item.experience }}
          </div>
          <div class="action-section">
            <div class="fx-cer gap-2">
              <!-- email -->
              <button
                class="w-10 h-10 border-1 border-[#EDEDED] rounded-2 fx-cer justify-center bg-transparent dark:border-[#323232]"
                @click.stop="openEmailModal(item.id)"
              >
                <img src="~/assets/image/email2.svg" alt="Network" class="btn-icon btn-icon-dark" />
                <img src="~/assets/image/email.svg" alt="Network" class="btn-icon btn-icon-light" />
              </button>
              <!-- id -->
              <button
                class="w-10 h-10 border-1 border-[#EDEDED] rounded-2 fx-cer justify-center bg-transparent dark:border-[#323232]"
                @click="() => {}"
              >
                <img
                  src="~/assets/image/id-card 3.svg"
                  alt="id-card"
                  class="btn-icon btn-icon-light"
                />
                <img
                  src="~/assets/image/id-card-dark.svg"
                  alt="id-card"
                  class="btn-icon btn-icon-dark"
                />
              </button>
            </div>
            <!-- 卡片下拉菜单 -->
            <button
              ref="dropdownTrigger"
              class="relative w-10 h-10 border-1 border-[#EDEDED] rounded-2 fx-cer justify-center bg-transparent dark:border-[#323232]"
              @click.stop="toggleDropdownMenu(item)"
            >
              <img src="~/assets/image/More.svg" alt="" />
              <div
                v-if="item.showDropdown"
                class="absolute right-0 top-10 mt-1 rounded shadow z-30 w-32 px-0 py-1 bg-white border border-gray-300 dark:(bg-gray-700 border-gray-600)"
              >
                <div
                  class="cursor-pointer fx-cer gap-2 px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-800 dark:text-white"
                  @click="openMoveToModal(item)"
                >
                  <img src="~/assets/image/Folder-close.svg" alt="" />
                  <span>Move to</span>
                </div>
              </div>
            </button>
          </div>
        </div>
      </template>
    </DragFolderCard>

    <!-- 删除模态框 -->
    <div
      v-if="showDeleteModal"
      class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50"
    >
      <div
        class="rounded-2 shadow-lg w-80 relative p-4 bg-white text-gray-900 dark:(bg-gray-800 text-white)"
      >
        <button
          class="absolute top-2 right-2 text-gray-500 bg-transparent hover:text-gray-700 dark:hover:text-gray-300"
          @click="closeModal"
        >
          <img src="~/assets/image/Close-small.svg" alt="" />
        </button>
        <div v-if="deleteError" class="fx-cer justify-center mb-4">
          <img src="~/assets/image/warn.svg" alt="" class="w-9 h-9" />
        </div>
        <h3 class="text-3.5 font-semibold mb-4 text-center">
          {{ deleteError ? 'Unable to delete the folder' : 'Delete the folder' }}
        </h3>
        <p class="text-sm mb-4 font-400 text-center mt-3">
          {{
            deleteError
              ? deleteError
              : 'Are you sure you want to delete the "Candidate" folder? This action will permanently delete the folder and its contents, and it cannot be undone.'
          }}
        </p>
        <div v-if="deleteError" class="text-center">
          <button
            class="mt-2 px-4 py-1 text-sm bg-black text-white rounded hover:bg-gray-900 w-full"
            @click="closeModal"
          >
            OK
          </button>
        </div>
        <div v-else class="flex justify-between space-x-2">
          <button
            class="px-4 py-1 flex-1 text-sm rounded bg-gray-200 text-gray-900 hover:bg-gray-300 dark:(bg-gray-600 text-white hover:bg-gray-700)"
            @click="closeModal"
          >
            Cancel
          </button>
          <button
            class="px-4 py-1 flex-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
            @click="onConfirmDelete"
          >
            Delete
          </button>
        </div>
      </div>
    </div>

    <!-- Move To Modal -->
    <Transition name="modal">
      <div
        v-if="moveToModalVisible"
        class="fixed inset-0 bg-black bg-opacity-30 flex items-end md:items-center justify-center z-50"
        @click.self="closeMoveToModal"
      >
        <div
          class="w-full md:w-[520px] max-h-[90vh] md:h-[470px] rounded-t-2 md:rounded-2 shadow-lg bg-[#F5F5F5] text-gray-900 dark:(bg-[#141415] text-white) relative p-4 md:p-6"
        >
          <!-- Header -->
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">Move to</h3>
            <button
              class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 bg-transparent"
              @click="closeMoveToModal"
            >
              <img src="~/assets/image/Close-small.svg" alt="" />
            </button>
          </div>

          <!-- Scrollable list -->
          <ul
            class="overflow-y-auto w-full max-h-[calc(100vh-220px)] border border-[#ccc] md:h-[320px] bg-white rounded-2 dark:bg-[#1E1E1E] dark:border-[#1E1E1E]"
          >
            <li
              class="px-4 py-1 h-12 fx-cer gap-4 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
              :class="{
                'bg-gray-300 text-black': selectedFolder?.id === talentFavoritesNode.id,
              }"
              @click="handleFolderClick(talentFavoritesNode)"
            >
              <img
                src="~/assets/image/star.png"
                alt=""
                class="w-8 h-8 border border-[#DDD] rounded btn-icon-light"
              />
              <img
                src="~/assets/image/Vector.svg"
                alt=""
                class="w-8 h-8 p-2 border border-[#3D3F40] rounded btn-icon-dark dark:bg-[#3D3F40]"
              />
              <span>Talents</span>
            </li>
            <li
              v-for="folder in data.filter(f => f.type === 'folder')"
              :key="folder.id"
              class="px-10 py-1 h-12 fx-cer gap-4 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
              :class="{
                'bg-gray-300 text-black': selectedFolder?.id === folder.id,
              }"
              @click="handleFolderClick(folder)"
            >
              <img src="~/assets/image/folder 1.svg" alt="" class="btn-icon-light" />
              <img src="~/assets/image/folder2.svg" alt="" class="btn-icon-dark" />
              <span>{{ folder.name }}</span>
            </li>
          </ul>

          <!-- Footer -->
          <div class="flex justify-between mt-4 gap-4">
            <button
              class="h-10 px-4 py-1 flex-1 text-sm rounded bg-white border border-gray-300 text-gray-900 hover:bg-gray-300 dark:(bg-transparent text-white hover:bg-gray-700 border-[#252525])"
              @click="closeMoveToModal"
            >
              Cancel
            </button>
            <button
              class="h-10 px-4 py-1 flex-1 text-sm rounded bg-black text-white hover:bg-black-300 dark:(bg-[#3B3B3B] text-white hover:bg-gray-700)"
              @click="moveItemToFolder(folder)"
            >
              Confirm
            </button>
          </div>
        </div>
      </div>
    </Transition>
    <Notification ref="notifier" />
    <!-- 邮箱模态框 -->
    <EmailModal v-model="showEmailModal" :user-id="currentUserId" />
    <!-- 卡片详情模态框 -->
    <CardDetailModal v-model="showCardModal" :user="selectedUser" />
  </div>
</template>

<script setup>
  import { reactive, ref, computed, defineProps } from 'vue'
  import DragFolderCard from './DragFolderCard.vue'
  import starFill from '~/assets/image/star-fill.svg'
  import starEmpty from '~/assets/image/star.png'
  import starFill2 from '~/assets/image/star-fill2.svg'
  import starEmpty2 from '~/assets/image/Star-dark.svg'
  import Notification from '@/components/SearchCard/Notification.vue'
  import EmailModal from '@/components/SearchCard/EmailModal.vue'
  import CardDetailModal from './CardDetailModal.vue'

  const currentFolder = ref(null)
  const showDeleteModal = ref(false)
  const folderToDelete = ref(null)
  const deleteError = ref('')
  const moveToModalVisible = ref(false)
  const selectedCardForMove = ref(null)
  const dropdownTrigger = ref([])
  const showCardModal = ref(false)
  const selectedUser = ref(null)
  const selectedFolder = ref(null)
  const notifier = ref()
  const isMobile = ref(false)

  const checkMobile = () => {
    isMobile.value = window.innerWidth <= 768
  }

  const props = defineProps({
    onGoToUpload: {
      type: Function,
      required: true,
    },
  })

  // 选中文件夹
  const handleFolderClick = folder => {
    selectedFolder.value = folder
  }

  const notify = message => {
    notifier.value?.add(message)
  }

  // 卡片详情
  function openCardModal(user) {
    selectedUser.value = user
    showCardModal.value = true
  }

  let idCounter = 1000
  const genId = () => idCounter++

  const talentFavoritesNode = {
    id: -1,
    type: 'folder',
    name: 'Talent favorites',
    isTalentFavorites: true, // 标记为顶级节点
  }

  const data = reactive([
    {
      id: genId(),
      type: 'card',
      name: 'Alice',
      avatar: 'https://i.pravatar.cc/40?img=1',
      isFavorited: true,
      title: 'LLM Specialist',
      tags: ['Multilingual NLP', 'Researcher'],
      experience: '7 yrs exp',
      company: 'Google',
    },
    {
      id: genId(),
      type: 'card',
      name: 'Bob',
      avatar: 'https://i.pravatar.cc/40?img=2',
      isFavorited: false,
      title: 'AI Engineer',
      tags: ['Deep Learning', 'ML Ops'],
      experience: '5 yrs exp',
      company: 'Google',
    },

    {
      id: genId(),
      type: 'folder',
      name: 'Team A',
      contents: reactive([
        {
          id: genId(),
          type: 'card',
          name: 'Alice',
          avatar: 'https://i.pravatar.cc/40?img=3',
          isFavorited: true,
          title: 'LLM Specialist',
          tags: ['Multilingual NLP', 'Researcher'],
          experience: '7 yrs exp',
          company: 'Google',
        },
        {
          id: genId(),
          type: 'card',
          name: 'Alice',
          avatar: 'https://i.pravatar.cc/40?img=4',
          isFavorited: true,
          title: 'LLM Specialist',
          tags: ['Multilingual NLP', 'Researcher'],
          experience: '7 yrs exp',
          company: 'Google',
        },
      ]),
      editing: false,
      showMenu: false,
    },
  ])

  const mainList = computed({
    get() {
      return currentFolder.value ? currentFolder.value.contents : data
    },
    set(val) {
      if (currentFolder.value) {
        currentFolder.value.contents = val
      } else {
        data.splice(0, data.length, ...val)
      }
    },
  })

  function addFolder() {
    const baseName = 'New Folder'
    let maxIndex = 0

    // 遍历现有文件夹名称
    data.forEach(item => {
      if (item.type === 'folder') {
        const name = item.name?.trim()

        if (name === baseName) {
          maxIndex = Math.max(maxIndex, 1)
        } else {
          const match = name.match(/^New Folder (\d+)$/)
          if (match) {
            const num = parseInt(match[1])
            if (!isNaN(num)) {
              maxIndex = Math.max(maxIndex, num)
            }
          }
        }
      }
    })

    const nextName = maxIndex === 0 ? baseName : `${baseName} ${maxIndex + 1}`

    const newFolder = {
      id: genId(),
      type: 'folder',
      name: nextName,
      contents: reactive([]),
      editing: false,
      showMenu: false,
    }
    data.unshift(newFolder)
  }

  function editFolderName(folder) {
    folder.editing = true
  }
  function saveFolderName(folder) {
    folder.editing = false
    if (!folder.name.trim()) folder.name = 'Unnamed Folder'
  }
  function enterFolder(folder) {
    currentFolder.value = folder
  }
  function exitFolder() {
    currentFolder.value = null
  }
  function toggleMenu(folder) {
    folder.showMenu = !folder.showMenu
  }

  function removeFromAll(item) {
    const id = item.id

    // 从根目录找
    const idx = data.findIndex(i => i.id === id)
    if (idx !== -1) return data.splice(idx, 1)

    // 从所有文件夹里找
    for (const folder of data.filter(f => f.type === 'folder')) {
      const i = folder.contents.findIndex(c => c.id === id)
      if (i !== -1) return folder.contents.splice(i, 1)
    }
  }

  function confirmDelete(folder) {
    folder.showMenu = false
    folderToDelete.value = folder
    deleteError.value = ''
    showDeleteModal.value = true
  }

  function closeModal() {
    showDeleteModal.value = false
    deleteError.value = ''
  }

  // 删除文件夹
  function onConfirmDelete() {
    if (folderToDelete.value.contents.length > 0) {
      deleteError.value =
        'The folder contains candidates and cannot be deleted. Please remove or delete all candidates from the folder before trying again.'
      return
    }
    const index = data.findIndex(f => f.id === folderToDelete.value.id)
    if (index !== -1) data.splice(index, 1)
    showDeleteModal.value = false
    if (currentFolder.value?.id === folderToDelete.value?.id) {
      currentFolder.value = null
    }
    folderToDelete.value = null
  }

  function toggleDropdownMenu(card) {
    const isOpen = card.showDropdown
    // 首先关闭所有其他卡片的菜单
    data.forEach(item => {
      if (item.type === 'folder') {
        item.contents.forEach(c => (c.showDropdown = false))
      }
      if (item.type === 'card') {
        item.showDropdown = false
      }
    })

    // 如果之前是关闭状态，则打开；如果已经是打开状态，就关闭（不再重新打开）
    if (!isOpen) {
      card.showDropdown = true
      selectedCardForMove.value = card
    } else {
      selectedCardForMove.value = null
    }
  }

  function openMoveToModal(card) {
    moveToModalVisible.value = true
    selectedCardForMove.value = card
    card.showDropdown = false // 关闭菜单
  }

  // 关闭移动到模态框
  function closeMoveToModal() {
    moveToModalVisible.value = false
    selectedFolder.value = null

    data.forEach(item => {
      if (item.type === 'card' && item.showDropdown) {
        item.showDropdown = false
      }
      if (item.type === 'folder') {
        item.contents.forEach(c => {
          if (c.type === 'card' && c.showDropdown) {
            c.showDropdown = false
          }
        })
      }
    })
  }

  function moveItemToFolder() {
    if (!selectedCardForMove.value) return

    // 从当前所在位置移除卡片
    removeFromAll(selectedCardForMove.value)

    if (selectedFolder.value.isTalentFavorites) {
      // 插入到根级数据顶部
      data.unshift({ ...selectedCardForMove.value })
    } else {
      // 插入目标文件夹
      selectedFolder.value.contents.push({ ...selectedCardForMove.value })
    }

    closeMoveToModal()
  }
  // 修改关注状态
  function toggleFavorite(card) {
    card.isFavorited = !card.isFavorited
    notify(card.isFavorited ? 'Candidate saved successfully' : 'Candidate has been removed')
  }

  const showEmailModal = ref(false)
  // 当前用户 ID
  const currentUserId = ref(null)

  // 打开email模态框
  const openEmailModal = userId => {
    currentUserId.value = userId
    showEmailModal.value = true
  }

  // 监听页面拖拽上传
  onMounted(() => {
    checkMobile()
    window.addEventListener('resize', checkMobile)

    // ========== 拖拽监听 ==========
    window.addEventListener('dragover', handleDragOver)
    window.addEventListener('drop', handleDrop)
  })

  onBeforeUnmount(() => {
    window.removeEventListener('resize', checkMobile)
    window.removeEventListener('dragover', handleDragOver)
    window.removeEventListener('drop', handleDrop)
  })

  function handleDragOver(event) {
    event.preventDefault()
  }

  function handleDrop(event) {
    event.preventDefault()

    const files = event.dataTransfer?.files
    if (!files || files.length === 0) return

    const fileList = Array.from(files)
    const validFiles = fileList.filter(f => f.type.startsWith('image/') || f.type || f.name)

    if (validFiles.length > 0) {
      // 跳转到上传页
      props.onGoToUpload()

      // ✅ 延迟一点时间等页面切换完再上传
      setTimeout(() => {
        // 使用全局事件或 Pinia 等传递 fileList
        window.dispatchEvent(
          new CustomEvent('start-upload', {
            detail: validFiles,
          })
        )
      }, 300)
    }
  }
</script>

<style scoped>
  .action-section {
    display: flex;
    width: 100%;
    flex-direction: row;
    gap: 16px;
    align-items: center;
    justify-content: space-between;
  }

  .btn-icon {
    width: 20px;
    height: 20px;
  }

  /* 按钮图标控制 */
  .btn-icon-dark {
    display: none;
  }

  .dark .btn-icon-light {
    display: none;
  }

  .dark .btn-icon-dark {
    display: inline-block;
  }

  /* 文件夹 */
  .folder-box {
    color: #333;
    border-radius: 10px;
    background-image: url('~/assets/image/folder-w1.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    z-index: 0;
  }

  .folder-box:hover {
    background-image: url('~/assets/image/folder-w2.png');
    background-size: 104% 107%;
    background-position: -6px -5px;
    background-repeat: no-repeat;
    border-radius: 12px;
  }

  .dark .folder-box {
    background-image: url('~/assets/image/folder-d1.png');
  }

  .dark .folder-box:hover {
    background-image: url('~/assets/image/folder-d2.png');
    background-size: 104% 107%;
    background-position: -6px -5px;
    background-repeat: no-repeat;
    border-radius: 12px;
  }

  @media (max-width: 768px) {
    .talent-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap; /* 保证空间不足时换行 */
      gap: 0.5rem;
    }

    .talent-title > div:first-child {
      margin-bottom: 0 !important; /* 移除标题与按钮之间的间距 */
    }

    .talent-title > .fx-cer {
      margin-bottom: 0;
    }
  }

  @media (max-width: 360px) {
    .talent-title {
      flex-direction: column;
      align-items: flex-start;
    }

    .talent-title > .fx-cer {
      width: 100%;
      flex-wrap: wrap;
      gap: 0.5rem;
    }
  }
</style>
