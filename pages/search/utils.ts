export function extractMidDollarValue(text: string | undefined) {
  if (!text) return 'Unknown'
  const regex = /\$(\d+(?:\.\d+)?)(?:\s*(million|billion|m|b))?/gi
  const matches = [...text.matchAll(regex)]

  if (matches.length === 0) return null

  // 将匹配结果转换为统一的数字（单位统一为 M）
  const values = matches.map(([, numStr, unit]) => {
    let num = parseFloat(numStr)
    let unitLower = unit?.toLowerCase()

    if (unitLower === 'billion' || unitLower === 'b') {
      num *= 1000
    }
    // 默认当作 million/M
    return num
  })

  // 如果是区间，取中间值
  if (values.length === 2) {
    const mid = (values[0] + values[1]) / 2
    return `$${mid.toFixed(0)} M`
  }  else if (values.length === 1) {
    return `$${values[0].toFixed(0)} M`
  } else {
    return 'Unknown'
  }
}