<template>
  <div
    v-if="visible"
    class="fixed inset-0 z-50 flex flex-col items-center justify-center bg-black bg-opacity-40"
    @click.self="close"
  >
    <div class="relative w-[520px] h-[340px] bg-white rounded-3" data-card-id="share-modal">
      <!-- 顶部导航 -->
      <div class="relative w-full h-[145px] back-linear flex items-center justify-center mb-4">
        <img src="~/assets/image/bgTri.png" alt="" class="absolute top-0 left-0 w-40 h-32" />
        <img
          src="~/assets/image/bgCircle.png"
          alt=""
          class="absolute bottom-[-60px] right-0 w-32 h-32"
        />
        <div class="w-[340px] flex items-center justify-between">
          <div class="fx-cer flex-col justify-center gap-2 text-sm text-[#C6C6C6]">
            <img :src="props.user.from_company_logo_url || defaultCompany" class="w-12.5 h-12.5 rounded-full" />
            <span>{{ props.user.from_company }}</span>
          </div>
          <div class="flex items-center flex-col justify-center">
            <span class="salary-text text-white">{{ extractMidDollarValue(props.user?.salary) }}</span>
            <img src="~/assets/image/fromto2.svg" alt="" class="w-[90px]" />
          </div>
          <div class="fx-cer flex-col justify-center gap-2 text-sm text-[#C6C6C6]">
            <img :src="props.user.to_company_logo_url || defaultCompany" class="w-12.5 h-12.5 rounded-full" />
            <span>{{ props.user.to_company }}</span>
          </div>
        </div>
      </div>
      <button class="absolute right-4 top-4 text-xl font-bold bg-transparent" @click="close">
        <img src="~/assets/image/Close-small2.svg" class="w-6 h-6 bg-transparent" />
      </button>
      <div class="px-6 relative">
        <img :src="props.user.avatar_url || defaultAvator" class="absolute top-[-50px] left-5 w-20 h-20 rounded-full" />
        <!-- 用户信息 -->
        <div class="mb-4">
          <div class="fx-cer justify-between">
            <div class="mt-8">
              <h2 class="text-lg font-semibold text-[#3C3C3C]">
                {{ props.user.person_name }} <span v-if="props.user?.nameCN">({{ props.user?.nameCN }})</span>
              </h2>
              <div>
                <p class="text-sm text-[#3c3c3c] line-clamp-1">{{ user?.talent_description }}</p>
              </div>
            </div>
            <div class="w-[80px] h-[80px]">
              <img src="/image/qrcode.png" alt="" class="min-w-[80px] h-[80px]" />
            </div>
          </div>
        </div>
      </div>
      <!-- footer -->
      <div class="border-t-[1px] border-[#F3F3F3] py-5 mx-5 flex justify-between items-center">
        <img src="/image/logo.png" alt="" />
        <div class="flex items-center gap-3" data-action-buttons>
          <!-- Download Button -->
          <button
            class="fx-cer bg-[#FFFFFF]/60! border border-gray-200! rounded-full py-2 px-4 text-gray-700! gap-2 transition-all duration-200 select-none min-h-[40px] cursor-pointer disabled:opacity-70 disabled:cursor-not-allowed hover:bg-[#F5F5F5]! disabled:hover:bg-[#FFFFFF]/60!"
            :disabled="isDownloading"
            @click="handleDownload"
            style="backdrop-filter: blur(34px);"
          >
            <div
              v-if="isDownloading"
              class="i-svg-spinners:3-dots-fade w-4 h-4 pointer-events-none"
            ></div>
            <div v-else class="i-material-symbols:download w-4 h-4 pointer-events-none"></div>
            <span class="text-sm font-medium pointer-events-none">{{ isDownloading ? 'Downloading...' : 'Download' }}</span>
          </button>

          <!-- Share Button -->
          <button
            class="fx-cer bg-[#FFFFFF]/60! border border-gray-200! rounded-full py-2 px-4 text-gray-700! gap-2 transition-all duration-200 select-none min-h-[40px] cursor-pointer hover:bg-[#F5F5F5]!"
            @click="handleShare"
            style="backdrop-filter: blur(34px);"
          >
            <div class="i-proicons:x-twitter w-4 h-4 pointer-events-none"></div>
            <span class="text-sm font-medium pointer-events-none">Share</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { defineProps, ref } from 'vue'
  import html2canvas from 'html2canvas-pro'
  import { extractMidDollarValue } from '~/pages/search/utils'
  import defaultCompany from '~/assets/image/defaultCompany.png'
  import defaultAvator from '~/assets/image/defaultAvator.png'

  const props = defineProps<{
    user: any
    visible: boolean
    onClose: () => void
  }>()

  const isDownloading = ref(false)

  const close = () => {
    props.onClose()
  }

  // 等待图片加载完成
  const waitForImageLoad = (img: HTMLImageElement): Promise<void> => {
    return new Promise((resolve) => {
      if (img.complete && img.naturalWidth > 0) {
        resolve()
      } else {
        img.onload = () => resolve()
        img.onerror = () => {
          console.warn('Image failed to load:', img.src)
          resolve() // 即使失败也继续
        }
      }
    })
  }

  // 下载功能
  const handleDownload = async () => {
    if (isDownloading.value || typeof window === 'undefined') return

    isDownloading.value = true
    try {
      await downloadFromScreenshot()
    } catch (error) {
      console.error('Download failed:', error)
      alert('Screenshot failed. Please try again or contact support.')
    } finally {
      isDownloading.value = false
    }
  }

  // 实时截图下载
  const downloadFromScreenshot = async () => {
    const elementToCapture = document.querySelector('[data-card-id="share-modal"]')
    if (!elementToCapture) {
      throw new Error('Share modal element not found')
    }

    // 等待所有图片加载完成
    const images = elementToCapture.getElementsByTagName('img')
    const imagePromises = [...images].map(img => waitForImageLoad(img))
    await Promise.all(imagePromises)

    // 等待字体加载完成
    if (document.fonts) {
      await document.fonts.ready
    }

    // 额外等待时间确保渲染完成
    await new Promise(resolve => setTimeout(resolve, 800))

    // 使用html2canvas-pro
    const canvas = await html2canvas(elementToCapture as HTMLElement, {
      backgroundColor: '#ffffff',
      scale: 2,
      useCORS: true,
      allowTaint: true,
      logging: false,
      imageTimeout: 15000,
      foreignObjectRendering: false,
      scrollX: 0,
      scrollY: 0,
      // 在截图时替换按钮为版权信息
      onclone: (clonedDoc) => {
        const clonedElement = clonedDoc.querySelector('[data-card-id="share-modal"]')
        if (clonedElement) {
          // 查找按钮容器并替换为版权信息
          const buttonContainer = clonedElement.querySelector('[data-action-buttons]')
          if (buttonContainer) {
            // 创建版权信息元素
            const copyrightDiv = clonedDoc.createElement('span')
            copyrightDiv.className = 'text-[#666666] text-sm font-400'
            copyrightDiv.textContent = 'Copyright @ 2025 DINQ Inc. All rights reserved'

            // 替换按钮容器为版权信息
            buttonContainer.parentNode?.replaceChild(copyrightDiv, buttonContainer)
          }
        }
      }
    })

    // 创建下载链接
    const link = document.createElement('a')
    link.download = `dinq-transfer-card-${Date.now()}.png`
    link.href = canvas.toDataURL('image/png', 1.0)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 分享功能
  const handleShare = () => {
    const shareText = `🚀 ${props.user.person_name} just made a major career move from ${props.user.from_company} to ${props.user.to_company}!

💰 Salary: ${extractMidDollarValue(props.user?.salary)}

Check out more talent moves on DINQ! 👇`

    const shareUrl = 'https://dinq.io'
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`
    window.open(twitterUrl, '_blank')
  }
</script>

<style scoped>
  .scroll-setting {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
  }

  .scroll-setting::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
  }

  .back-linear {
    background: linear-gradient(96deg, #281812 0.42%, #120f2a 73.12%);
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
  }

  .salary-text {
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    font-size: 20px;
  }
</style>