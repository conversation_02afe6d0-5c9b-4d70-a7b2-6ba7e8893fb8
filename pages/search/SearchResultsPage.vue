<template>
  <div
    class="relative flex flex-col min-h-screen overflow-hidden"
    :class="{ 'pt-10': isMobile }"
  >
    <!-- 背景图 -->
    <div class="absolute top-0 left-0 w-[436px] h-[469px]">
      <img src="~/assets/image/bgTri.png" alt="" class="w-full h-full object-contain" />
    </div>
    <div class="absolute bottom-[-160px] right-0 w-[351px] h-[443px]">
      <img src="~/assets/image/bgCircle.png" alt="" class="w-full h-full object-contain" />
    </div>

    <!-- 错误提示区域 -->
    <div v-if="showError" class="w-full max-w-[760px] mb-6 px-4">
      <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800 dark:text-red-400">
              Search Error
            </h3>
            <div class="mt-2 text-sm text-red-700 dark:text-red-300">
              {{ error }}
            </div>
          </div>
          <div class="ml-auto pl-3">
            <div class="-mx-1.5 -my-1.5">
              <button @click="clearError" class="inline-flex bg-red-50 dark:bg-red-900/20 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/40 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600">
                <span class="sr-only">Dismiss</span>
                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 - 使用 flex-1 占据剩余空间 -->
    <div class="flex-1 flex flex-col items-center justify-center px-4">
      <!-- 数据卡片展示区域 -->
      <div class="w-full max-w-[760px] space-y-4">
        <div class="talent-card-wrapper">
          <SearchCard :candidates="cards" />
        </div>
      </div>
    </div>

    <!-- 搜索框 - 固定在底部 -->
    <div class="w-full px-4 pb-4 relative z-20">
      <div
        class="w-full max-w-[760px] mx-auto h-auto border border-black dark:border-[#494949] rounded-[10px] bg-white px-4 py-3 dark:bg-[#242425] relative z-20"
      >
        <input
          v-model="inputValue"
          @keyup.enter="submitQuery"
          class="w-full px-2 py-2 text-sm sm:text-base rounded focus:outline-none focus:border-none mb-2 dark:bg-[#242425] placeholder:dark:text-[#686868] placeholder:dark:opacity-60"
          placeholder="AI Agent"
        />
        <div class="flex flex-wrap items-center justify-between gap-3">
          <!-- 筛选按钮 -->
          <div class="relative inline-block text-left">
            <button
              @click="toggleDropdown"
              class="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-200 bg-transparent dark:hover:bg-gray-200/20 border border-gray-300 dark:border-[#494949]"
            >
              <img :src="getCurrentIcon()" alt="" class="w-4 h-4 dark:brightness-0 dark:invert" />
              <span class="text-sm text-gray-700 dark:text-gray-300">{{ selected }}</span>
              <svg class="w-4 h-4 text-gray-500 transition-transform" :class="{ 'rotate-180': dropdownOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>

            <!-- 筛选下拉菜单 - 向上弹出 -->
            <div
              v-if="dropdownOpen && !isMobile"
              class="absolute z-30 bottom-full mb-2 w-44 flex flex-col gap-2 bg-white p-2 border border-[#DDD] dark:border-[#252525] rounded-2 shadow-md dark:bg-[#1B1B1B]"
            >
              <div
                v-for="option in options"
                :key="option.value"
                @click="toggleSelection(option.value)"
                class="px-4 py-2 flex items-center gap-2 cursor-pointer w-full rounded-2"
                :class="{
                  'bg-[#FFF5F1] font-semibold text-[#CB7C5D] border border-[#E5C7BB] dark:bg-[#353535] dark:text-white dark:border-none':
                    selected === option.value,
                  'bg-transparent hover:bg-gray-100 text-gray-700 dark:text-gray-300 dark:hover:bg-gray-700':
                    selected !== option.value,
                }"
              >
                <img :src="option.icon" alt="" class="w-4 h-4 dark:brightness-0 dark:invert" />
                <span class="text-sm">{{ option.label }}</span>
                <svg v-if="selected === option.value" class="w-4 h-4 ml-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div>

          <!-- 移动端底部弹出 -->
          <Transition name="fade">
            <div
              v-if="dropdownOpen && isMobile"
              class="fixed bottom-0 left-0 right-0 bg-white dark:bg-[#1B1B1B] z-50 rounded-t-[20px] p-4 border-t border-[#DDD] dark:border-[#2B2B2B] shadow-xl"
            >
              <!-- 关闭按钮 -->
              <div class="flex justify-end mb-4">
                <button
                  class="text-gray-500 dark:text-gray-300 bg-transparent"
                  @click="dropdownOpen = false"
                >
                  <img src="~/assets/image/close-middle.svg" alt="" class="w-5 h-5" />
                </button>
              </div>

              <!-- 筛选项内容 -->
              <div class="flex flex-col gap-2">
                <div
                  v-for="option in options"
                  :key="option.value"
                  @click="toggleSelection(option.value)"
                  class="px-4 py-3 flex items-center gap-2 cursor-pointer rounded-2"
                  :class="{
                    'bg-[#FFF5F1] font-semibold text-[#CB7C5D] border border-[#E5C7BB] dark:bg-[#353535] dark:text-white dark:border-none':
                      selected === option.value,
                    'bg-white border border-[#E6E6E6] hover:bg-gray-50 text-gray-700 dark:border-none dark:bg-[#212121] dark:hover:bg-[#353535] dark:text-[#7A7A7A]':
                      selected !== option.value,
                  }"
                >
                  <img :src="option.icon" alt="" class="dark:brightness-0 dark:invert" />
                  <span>{{ option.label }}</span>
                </div>
              </div>
            </div>
          </Transition>

          <!-- 提交按钮 -->
          <button
            class="btn btn-primary w-[70px] h-[36px] rounded-2"
            :class="{
              'bg-[#1C1C21] text-white': isInputValid,
              'bg-[#E5E7EB] cursor-not-allowed text-[#9CA3AF] dark:bg-[#323232] dark:text-[#5B5B5B]': !isInputValid,
            }"
            :disabled="!isInputValid"
            @click="submitQuery"
          >
            DINQ
          </button>
        </div>
      </div>
    </div>

    <!-- 加载弹窗 -->
    <div
      v-if="loading"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div
        class="bg-white dark:bg-black dark:border-[#27282D] p-6 rounded-lg shadow-xl w-[90%] max-w-[360px] h-[198px] flex flex-col items-center justify-center gap-2 text-center"
      >
        <p class="text-base font-bold sm:text-6">DINQing</p>
        <p class="text-[#585858] text-sm">Searching talent</p>
        <img
          src="~/assets/image/gif2.gif"
          alt="loading"
          class="btn-icon-dark w-16 h-16 sm:w-20 sm:h-20"
        />
        <img
          src="~/assets/image/gif.gif"
          alt="loading"
          class="btn-icon-light w-16 h-16 sm:w-20 sm:h-20"
        />
      </div>
    </div>
  </div>

  <!-- 429错误弹窗 -->
  <RateLimitModal
    v-model:visible="showRateLimitModal"
    @upgrade="handleUpgrade"
  />
</template>

<script setup lang="ts">
  import { ref, computed, defineExpose, onMounted, onUnmounted, watch } from 'vue'
  import SearchCard from '../../components/SearchCard/index.vue'
  import RateLimitModal from '../../components/RateLimitModal/index.vue'
  import { useFirebaseAuth } from '~/composables/useFirebaseAuth'
  import github from '/assets/image/github 1.svg'
  import scholar from '/assets/image/scholar 1.svg'
  import company from '/assets/image/office-building 1.svg'
  import 'animate.css'

  // Props
  const props = defineProps<{
    initialCards?: any[]
    initialQuery?: string
    initialFilter?: string
  }>()

  // 定义 emit
  const emit = defineEmits<{
    switchToPoachub: []
    backToSearch: []
  }>()

  // 控制状态
  const dropdownOpen = ref(false)
  const selected = ref<string>(props.initialFilter || 'Scholar')
  const inputValue = ref(props.initialQuery || '')
  const loading = ref(false)
  const cards = ref<Card[]>(props.initialCards || [])
  
  // 错误状态管理
  const error = ref('')
  const showError = ref(false)
  const showRateLimitModal = ref(false)
  
  // 检测深色模式
  const isDark = ref(false)
  const isMobile = ref(false)

  // 格式化数字，添加千分分隔符
  const formatNumber = (num: number | string): string => {
    if (typeof num === 'string') {
      const parsedNum = parseInt(num, 10)
      return isNaN(parsedNum) ? num : parsedNum.toLocaleString()
    }
    return num.toLocaleString()
  }

  // 检测移动端和深色模式
  onMounted(() => {
    const checkMobile = () => {
      isMobile.value = window.innerWidth <= 768
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
  })

  onMounted(() => {
    isDark.value = document.documentElement.classList.contains('dark')
    const observer = new MutationObserver(() => {
      isDark.value = document.documentElement.classList.contains('dark')
    })
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    })
  })

  // 筛选选项
  const options = [
    { value: 'Scholar', label: 'Scholar', icon: scholar },
    { value: 'GitHub', label: 'GitHub', icon: github },
    { value: 'Company', label: 'Company', icon: company },
  ]

  // 计算属性
  const isInputValid = computed(() => {
    return inputValue.value.trim().length > 0
  })

  // 获取当前图标
  const getCurrentIcon = () => {
    const option = options.find(opt => opt.value === selected.value)
    return option ? option.icon : scholar
  }

  // 切换下拉菜单
  const toggleDropdown = () => {
    dropdownOpen.value = !dropdownOpen.value
  }

  // 切换选择
  const toggleSelection = (value: string) => {
    selected.value = value
    dropdownOpen.value = false
  }

  // 清除错误
  const clearError = () => {
    showError.value = false
    error.value = ''
  }

  // Firebase Auth
  const { currentUser } = useFirebaseAuth()

  // 提交查询
  const submitQuery = async () => {
    if (!isInputValid.value) return

    // 清除之前的错误
    clearError()

    // 检查用户是否已登录
    if (!currentUser.value) {
      error.value = 'Please log in to search for talent. Authentication is required to access our database.'
      showError.value = true
      return
    }

    loading.value = true

    try {
      // 调用真实API
      const apiResponse = await fetchData(inputValue.value, selected.value)

      if (!apiResponse || apiResponse.length === 0) {
        error.value = `No results found for "${inputValue.value}" in ${selected.value}. Try different keywords or search category.`
        showError.value = true
        return
      }

      // 更新当前页面的搜索结果
      cards.value = apiResponse

      // 发射search成功事件，通知其他组件更新credits
      const { $emitter } = useNuxtApp()
      $emitter.emit('search-success')
    } catch (err: any) {
      console.error('Search failed:', err)

      // 根据错误类型显示不同的错误信息
      if (err.message?.includes('401') || err.message?.includes('unauthorized')) {
        error.value = 'Authentication failed. Please log out and log in again.'
      } else if (err.message?.includes('403') || err.message?.includes('forbidden')) {
        error.value = 'Access denied. You may not have permission to perform this search.'
      } else if (err.message?.includes('429') || err.message?.includes('rate limit') || err.message?.includes('usage limit') || err.message?.includes('Usage limit exceeded')) {
        // 显示429错误弹窗而不是红色toast
        showRateLimitModal.value = true
      } else if (err.message?.includes('500') || err.message?.includes('server error')) {
        error.value = 'Server error occurred. Please try again later.'
      } else if (err.message?.includes('network') || err.message?.includes('fetch')) {
        error.value = 'Network error. Please check your internet connection and try again.'
      } else {
        error.value = 'Search failed. Please try again or contact support if the problem persists.'
      }
      showError.value = true
    } finally {
      loading.value = false
      // 不清空输入值，让用户可以修改并继续搜索
    }
  }

  // 真实 API 请求函数
  const fetchData = async (query: string, filters: string): Promise<Card[]> => {
    if (!currentUser.value) {
      throw new Error('User not authenticated')
    }

    try {
      // 检查是否包含作弊码
      const hasCheatCode = query.toLowerCase().includes('d1nq')
      // 如果包含作弊码，从搜索内容中移除它
      const cleanQuery = hasCheatCode ? query.replace(/d1nq/gi, '').trim() : query

      // 构建查询参数
      const categoryMap: Record<string, string> = {
        'Scholar': 'type:scholar',
        'GitHub': 'type:github',
        'Company': 'group:company'
      }

      const category = categoryMap[filters] || 'type:scholar'
      const searchQuery = `${category} ${cleanQuery}`

      // 构建API URL
      const url = new URL('/api/v1/talent/search', window.location.origin)
      url.searchParams.append('text', searchQuery)

      // 如果包含作弊码，自动添加测试激活码
      if (hasCheatCode) {
        url.searchParams.append('code', '64696E71')
      }

      console.log('Making search API request:', {
        url: url.toString(),
        query: searchQuery,
        category,
        filters
      })

      // 发送请求
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${currentUser.value.uid}`,
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}`
        try {
          const errorData = await response.json()
          errorMessage = errorData.message || errorData.error || errorMessage
        } catch {
          const errorText = await response.text()
          errorMessage = errorText || errorMessage
        }
        throw new Error(errorMessage)
      }

      const apiData = await response.json()
      console.log('API Response:', apiData)

      // 转换API响应为前端期望的格式
      return transformApiResponse(apiData, filters)

    } catch (error) {
      console.error('API request error:', error)
      throw error
    }
  }

  // 数据转换函数
  const transformApiResponse = (apiData: any, searchType: string): Card[] => {
    if (!apiData?.data || !Array.isArray(apiData.data)) {
      console.warn('Invalid API response format:', apiData)
      return []
    }

    return apiData.data.map((item: any) => {
      const data = item.data || {}
      const profile = item.profile || {}

      // 根据搜索类型和dataset类型决定数据映射
      const dataset = profile.dataset || item.dataset || item.builder
      const isGitHub = dataset === 'github' || searchType === 'GitHub'
      const isCompany = profile.group === 'company' || item.group === 'company' || searchType === 'Company'

      // 处理不同数据源的字段映射
      let title: string, venue: string, status: string, year: string

      if (isGitHub) {
        // GitHub数据映射
        let rawTitle = data.repository || `${data.login}'s Repository` || 'GitHub Repository'
        title = rawTitle.startsWith('/') ? rawTitle.substring(1) : rawTitle
        venue = data.stars ? `⭐ ${formatNumber(data.stars)} stars` : 'GitHub'
        status = 'GitHub Repository'
        year = ''
      } else {
        // Scholar数据映射
        title = data.article || data.name || 'Research Work'
        venue = data.venue || 'Research'
        status = data.status || 'Published'
        year = String(data.year || new Date().getFullYear())
      }

      // 处理姓名
      const name = profile.name || data.name || data.login || 'Unknown'

      // 处理职位和机构
      let combinedPosition = ''
      let institution = ''

      if (isCompany) {
        combinedPosition = profile.position || 'Professional'
        institution = profile.company || data.company || 'Company'
      } else if (isGitHub) {
        combinedPosition = 'Developer'
        institution = data.company || 'GitHub'
      } else {
        combinedPosition = profile.position || 'Researcher'
        institution = profile.affiliation || data.affiliation || 'Research Institution'
      }

      // 处理头像
      const avatarUrl = profile.avatar_url || data.avatar_url || '/image/avator.png'

      // 处理技能/研究领域
      const researchAreas = profile.research_areas || data.research_areas || []

      return {
        // 保留原始API数据结构，供Network按钮使用
        dataset: profile.dataset || item.dataset,
        builder: profile.dataset || item.dataset || item.builder,
        group: profile.group || item.group,
        data: data,
        profile: profile,

        // 转换后的数据，供SearchCard显示使用
        data_type: isGitHub ? 'github' : 'paper',
        id: data.id || profile.id || '',
        name: name,
        positionTitle: combinedPosition,
        institution: institution,
        avatarUrl: avatarUrl,
        skills: researchAreas,
        summary: profile.summary?.description || profile.summary || '',
        featuredWork: {
          title,
          venue,
          type: status,
          year,
        },
        recommendations: ['Matched based on search criteria'],
        matchScore: 85,

        // 兼容旧格式
        author_ids: data.id || profile.id || '',

        // 原始数据结构（保留用于调试）
        _originalData: item
      }
    })
  }

  // 处理升级按钮点击
  const handleUpgrade = () => {
    // 跳转到订阅页面
    navigateTo('/subscription')
  }

  // 定义卡片类型
  interface Card {
    id: string
    name: string
    positionTitle: string
    institution: string
    avatarUrl: string
    skills: string[]
    featuredWork: {
      title: string
      venue: string
      type: string
      year: string
    }
    recommendations: string[]
    matchScore: number
    author_ids?: string
    _originalData?: any
  }

  // 暴露重置方法
  const resetData = () => {
    inputValue.value = ''
    selected.value = 'Scholar'
    cards.value = []
  }

  defineExpose({ resetData })
</script>

<style scoped>
  /* 按钮图标控制 */
  .btn-icon-dark {
    display: none;
  }

  .dark .btn-icon-light {
    display: none;
  }

  .dark .btn-icon-dark {
    display: block;
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: all 0.3s ease;
  }
  .fade-enter-from,
  .fade-leave-to {
    transform: translateY(100%);
    opacity: 0;
  }
  .fade-enter-to,
  .fade-leave-from {
    transform: translateY(0%);
    opacity: 1;
  }

  /* Poach Window 样式 */
  .poach-window-glass {
    background: #FFFFFF;
    border: 1px solid #E0E0DE;
    border-radius: 15px;
  }

  /* 深色模式下的样式 */
  .dark .poach-window-glass {
    background: transparent;
    border: 1px solid #27272A;
    border-image-source: none;
    border-image-slice: initial;
    backdrop-filter: none;
  }
</style>
