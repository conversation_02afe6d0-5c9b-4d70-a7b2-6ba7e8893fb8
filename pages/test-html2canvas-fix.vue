<template>
  <div class="container mx-auto p-8">
    <h1 class="text-3xl font-bold mb-6">html2canvas Hidden Element Fix Test</h1>

    <div class="space-y-6">
      <!-- Test buttons -->
      <div class="flex gap-4">
        <button
          @click="testOldMethod"
          class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
        >
          Test Old Method (will generate white image)
        </button>
        <button
          @click="testNewMethod"
          class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          Test New Method (should work normally)
        </button>
        <button
          @click="testVisibleElement"
          class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Test Visible Element (control group)
        </button>
      </div>

      <!-- Results display -->
      <div v-if="results.length > 0" class="space-y-4">
        <h2 class="text-xl font-semibold">Test Results:</h2>
        <div v-for="(result, index) in results" :key="index" class="border rounded p-4">
          <h3 class="font-medium mb-2">{{ result.method }}</h3>
          <div class="flex gap-4">
            <div>
              <p class="text-sm text-gray-600 mb-2">Generated Image:</p>
              <img :src="result.imageUrl" alt="Generated" class="border max-w-xs" />
            </div>
            <div class="flex-1">
              <p class="text-sm"><strong>Status:</strong> {{ result.status }}</p>
              <p class="text-sm"><strong>Dimensions:</strong> {{ result.dimensions }}</p>
              <p class="text-sm"><strong>Duration:</strong> {{ result.duration }}ms</p>
              <p v-if="result.error" class="text-sm text-red-600"><strong>Error:</strong> {{ result.error }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Test card elements -->

    <!-- Old method: using opacity: 0 + visibility: hidden -->
    <div
      ref="oldMethodCard"
      class="old-method-hidden"
      style="position: fixed; left: -9999px; top: -9999px; opacity: 0; visibility: hidden; z-index: -1;"
    >
      <div class="test-card" data-method="old">
        <h2>Old Method Test Card</h2>
        <p>This card uses opacity: 0 and visibility: hidden</p>
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded">
          <p>Gradient Background Test</p>
        </div>
      </div>
    </div>

    <!-- New method: using transform: translateX(-100%) -->
    <div
      ref="newMethodCard"
      class="new-method-hidden"
      style="position: fixed; top: 0; left: 0; transform: translateX(-100%); z-index: -1; pointer-events: none;"
    >
      <div class="test-card" data-method="new">
        <h2>New Method Test Card</h2>
        <p>This card uses transform: translateX(-100%)</p>
        <div class="bg-gradient-to-r from-green-500 to-blue-600 text-white p-4 rounded">
          <p>Gradient Background Test</p>
        </div>
      </div>
    </div>

    <!-- Visible element control group -->
    <div ref="visibleCard" class="mt-8">
      <h2 class="text-lg font-semibold mb-4">Visible Element Control Group:</h2>
      <div class="test-card" data-method="visible">
        <h2>Visible Test Card</h2>
        <p>This card is completely visible and serves as a control group</p>
        <div class="bg-gradient-to-r from-orange-500 to-red-600 text-white p-4 rounded">
          <p>Gradient Background Test</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import html2canvas from 'html2canvas-pro'

const oldMethodCard = ref<HTMLElement>()
const newMethodCard = ref<HTMLElement>()
const visibleCard = ref<HTMLElement>()

const results = ref<Array<{
  method: string
  imageUrl: string
  status: string
  dimensions: string
  duration: number
  error?: string
}>>([])

const captureElement = async (element: HTMLElement, methodName: string) => {
  const startTime = Date.now()

  try {
    console.log(`Starting screenshot: ${methodName}`)

    // Wait for rendering
    await new Promise(resolve => setTimeout(resolve, 100))

    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: 400,
      height: 300,
      logging: true,
    })

    const imageUrl = canvas.toDataURL('image/png')
    const duration = Date.now() - startTime

    // Check if it's a white image (simple detection)
    const isWhiteImage = imageUrl.length < 5000 // White images are usually small

    results.value.push({
      method: methodName,
      imageUrl,
      status: isWhiteImage ? 'Possibly white image' : 'Normal',
      dimensions: `${canvas.width}x${canvas.height}`,
      duration,
    })

    console.log(`${methodName} completed:`, {
      size: imageUrl.length,
      dimensions: `${canvas.width}x${canvas.height}`,
      duration
    })

  } catch (error: any) {
    console.error(`${methodName} failed:`, error)
    results.value.push({
      method: methodName,
      imageUrl: '',
      status: 'Failed',
      dimensions: '0x0',
      duration: Date.now() - startTime,
      error: error.message
    })
  }
}

const testOldMethod = () => {
  if (oldMethodCard.value) {
    const cardElement = oldMethodCard.value.querySelector('.test-card') as HTMLElement
    captureElement(cardElement, 'Old Method (opacity: 0 + visibility: hidden)')
  }
}

const testNewMethod = () => {
  if (newMethodCard.value) {
    const cardElement = newMethodCard.value.querySelector('.test-card') as HTMLElement
    captureElement(cardElement, 'New Method (transform: translateX(-100%))')
  }
}

const testVisibleElement = () => {
  if (visibleCard.value) {
    const cardElement = visibleCard.value.querySelector('.test-card') as HTMLElement
    captureElement(cardElement, 'Visible Element Control Group')
  }
}

// Set page meta
useSeoMeta({
  title: 'html2canvas Hidden Element Fix Test - DINQ',
  description: 'Test html2canvas screenshot effects on hidden elements',
})
</script>

<style scoped>
.test-card {
  width: 400px;
  height: 300px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
  font-family: 'Arial', sans-serif;
}

.test-card h2 {
  font-size: 24px;
  margin-bottom: 16px;
  font-weight: bold;
}

.test-card p {
  font-size: 16px;
  margin-bottom: 20px;
  line-height: 1.5;
}
</style>
