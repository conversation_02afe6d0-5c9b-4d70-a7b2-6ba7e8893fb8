<template>
  <div class="talent-pool-page">

    <!-- 自定义背景覆盖层 -->
    <div class="custom-bg-overlay"></div>
    
    <!-- 背景图片 -->
    <div class="bg-tri">
      <img src="~/assets/image/bgTri.png" alt="Background Triangle" class="w-full h-full object-contain" />
    </div>
    <div class="bg-circle">
      <img src="~/assets/image/bgCircle.png" alt="Background Circle" class="w-full h-full object-contain" />
    </div>
    
    <!-- 初始搜索页 -->
    <div v-if="searchState === 'initial'" class="initial-search-page">
      <div class="header-section">
        <div class="header-container">
          <div class="logo">
            <img 
              :src="currentTheme === 'dark' ? '/image/logo-big-dark.svg' : '/image/logo-big.svg'" 
              alt="Logo" 
              class="w-full h-full object-contain" 
            />
          </div>
          <h1 class="title clash-semibold text-primary-100">Talent Pool</h1>
        </div>
        <div class="subtitle clash-medium">Intelligent matching of top talent</div>
      </div>
      
      <div class="search-section">
        <div class="search-container">
          <TalentPoolBar @search="handleSearch" />
        </div>
        <div class="filter-container">
          <span class="filter-text">First author only</span>
          <div class="toggle-switch">
            <label class="switch">
              <input type="checkbox" v-model="firstAuthorOnly">
              <span class="slider"></span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- 无结果页 -->
    <div v-else-if="searchState === 'no-results'" class="no-results-page">
      <div class="search-section">
        <div class="search-container">
          <TalentPoolBar :initial-query="searchQuery" @search="handleSearch" />
        </div>
        <div class="filter-container">
          <span class="filter-text">First author only</span>
          <div class="toggle-switch">
            <label class="switch">
              <input type="checkbox" v-model="firstAuthorOnly">
              <span class="slider"></span>
            </label>
          </div>
        </div>
      </div>
      
      <div class="no-result-content">
        <div class="no-result-image">
          <img 
            :src="currentTheme === 'dark' ? '/image/noResult-dark.png' : '/image/noResult.png'" 
            alt="No Results" 
          />
        </div>
        <div class="no-result-text">
          <p><span class="sorry">Sorry</span>, we couldn't find any matches for your search<br>
          Try broadening your search terms or checking the spelling</p>
        </div>
      </div>
      
      <div class="support-section">
        <div class="support-info">
          <p>Need help? Contact our support team at <a href="mailto:<EMAIL>" class="support-email"><EMAIL></a></p>
        </div>
      </div>
    </div>
    
    <!-- 有结果页 -->
    <div v-else-if="searchState === 'results'" class="results-page">
      <div class="search-section">
        <div class="search-container">
          <TalentPoolBar :initial-query="searchQuery" @search="handleSearch" />
        </div>
        <div class="filter-container">
          <span class="filter-text">First author only</span>
          <div class="toggle-switch">
            <label class="switch">
              <input type="checkbox" v-model="firstAuthorOnly">
              <span class="slider"></span>
            </label>
          </div>
        </div>
      </div>
      
      <div class="results-section">
        <div class="results-summary">
          Based on your requirements, we have found <span class="highlight-number">{{ filteredResults.length }}</span> candidates.
        </div>
        <div class="talent-card-wrapper">
          <TalentCard :candidates="filteredResults" />
        </div>
      </div>
    </div>

    <!-- 加载弹窗 -->
    <TalentPoolLoading ref="loadingModalRef" v-model:visible="loading" />
    
    <!-- 429错误弹窗 -->
    <RateLimitModal 
      v-model:visible="showRateLimitModal" 
      @upgrade="handleUpgrade"
    />
  </div>
</template>

<script setup lang="ts">
// 导入自定义搜索组件
import TalentPoolBar from '../../components/SearchBar/TalentPoolBar.vue'
import TalentCard from '../../components/TalentCard/TalentCard.vue'
import TalentPoolLoading from '../../components/Loading/TalentPoolLoading.vue'
import RateLimitModal from '../../components/RateLimitModal/index.vue'
// 导入API相关类型和函数
import { recommendPapers, type RecommendPapersParams } from '~/api/recommend'
import type {
  NewRecommendPapersResponse,
  NewRecommendedPaper,
  RecommendDataItem,
  GitHubData
} from '~/api/types'

// 导入用户认证和事件发射器
const { currentUser } = useFirebaseAuth()
const { $emitter } = useNuxtApp()

// 定义搜索结果类型
interface FeaturedWork {
  title: string
  venue: string
  type: string
  year?: string
}

interface SearchResult {
  id: string
  name: string
  positionTitle: string
  institution: string
  avatarUrl?: string
  skills: string[]
  featuredWork: FeaturedWork
  recommendations: string[]
  matchScore: number
  author_ids?: string  // 添加OpenReview ID字段
  paperCount?: number
  citations?: number
  isFirstAuthor?: boolean
}

// 状态管理
const searchState = ref('initial') // 'initial', 'results', 'no-results'
const searchQuery = ref('')
const firstAuthorOnly = ref(false)
const loading = ref(false)
const showRateLimitModal = ref(false)

// 加载弹窗引用
const loadingModalRef = ref()

// 主题管理
const currentTheme = ref('light')

// 检测主题变化
onMounted(() => {
  // 初始化主题
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme) {
    currentTheme.value = savedTheme
  } else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    currentTheme.value = 'dark'
  }
  
  // 监听主题变化
  const observer = new MutationObserver(() => {
    const isDark = document.documentElement.classList.contains('dark')
    currentTheme.value = isDark ? 'dark' : 'light'
  })
  observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })
  
  // 清理观察器
  onUnmounted(() => {
    observer.disconnect()
  })
})

// 搜索结果数据
const searchResults = ref<SearchResult[]>([])

// 过滤后的结果
const filteredResults = computed(() => {
  // API已经根据only_first_author参数返回了正确的结果，不需要前端再次过滤
  return searchResults.value
})

// 将API响应转换为SearchResult格式
const transformApiResponse = (apiData: RecommendDataItem[]): SearchResult[] => {
  if (!Array.isArray(apiData)) {
    return []
  }
  
  // 格式化数字，添加千分分隔符
  const formatNumber = (num: number | string): string => {
    if (typeof num === 'string') {
      const parsedNum = parseInt(num, 10);
      return isNaN(parsedNum) ? num : parsedNum.toLocaleString();
    }
    return num.toLocaleString();
  };

  const results: SearchResult[] = []
  
  // 处理paper类型数据
  const paperData = apiData.filter(item => item.data_type === 'paper')
  if (paperData.length > 0) {
    const paperResults = paperData.map(item => {
      const paper = item.data as NewRecommendedPaper
      const author = paper.author_info
      
      // Handle recommend_reason
      let recommendationsArray: string[]
      if (Array.isArray(author.recommend_reason)) {
        recommendationsArray = author.recommend_reason.filter((reason: string) => reason.trim())
      } else if (typeof author.recommend_reason === 'string' && author.recommend_reason) {
        const sentences = author.recommend_reason.match(/[^.!?]+[.!?]+/g)
        recommendationsArray = sentences ? sentences.map((s: string) => s.trim()) : [author.recommend_reason.trim()]
        if (recommendationsArray.length === 0 && author.recommend_reason.trim()) {
          recommendationsArray = [author.recommend_reason.trim()]
        }
      } else {
        recommendationsArray = ['No specific recommendation reason available.']
      }
      
      // 组合职位和机构信息
      const position = author.position || 'Researcher'
      const institution = author.institution || ''
      const combinedPosition = institution ? `${position}, ${institution}` : position

      return {
        id: author.author_id,
        name: author.author,
        positionTitle: combinedPosition,
        institution: institution,
        avatarUrl: author.avatar_url,
        skills: paper.tags || [],
        featuredWork: {
          title: paper.title,
          venue: paper.source,
          type: paper.status,
          year: paper.year
        },
        recommendations: recommendationsArray,
        matchScore: author.score,
        author_ids: author.author_id, // 保存author_id用于分析页面跳转
        paperCount: author.paper_count || 0,
        citations: author.citations || 0,
        isFirstAuthor: paper.first_author_papers ? paper.first_author_papers > 0 : false
      }
    })
    
    results.push(...paperResults)
  }
  
  // 处理github类型数据
  const githubData = apiData.filter(item => item.data_type === 'github')
  if (githubData.length > 0) {
    const githubResults = githubData.map(item => {
      const githubData = item.data as GitHubData
      
      return {
        id: githubData.login,
        name: githubData.name || githubData.login,
        positionTitle: githubData.position || 'GitHub Developer',
        institution: `GitHub (@${githubData.login})`,
        avatarUrl: githubData.avatar_url,
        skills: githubData.tags || [],
        featuredWork: {
          title: (githubData.repository.name || 'Repository').startsWith('/')
            ? (githubData.repository.name || 'Repository').substring(1)
            : (githubData.repository.name || 'Repository'),
          venue: `⭐ ${formatNumber(githubData.repository.stars)} stars`,
          type: 'GitHub Repository',
          year: undefined
        },
        recommendations: githubData.recommend_reason || [],
        matchScore: githubData.score,
        author_ids: githubData.login, // 使用GitHub login作为ID
        paperCount: githubData.repository.stars || 0, // 用stars代替论文数量
        citations: 0, // GitHub数据没有引用数
        isFirstAuthor: true // GitHub项目通常是主要贡献者
      }
    })
    
    results.push(...githubResults)
  }
  
  return results.sort((a, b) => {
    // 按照matchScore降序排列（分数高的在前）
    if (b.matchScore !== a.matchScore) {
      return b.matchScore - a.matchScore;
    }
    // 如果分数相同，按名字字母顺序排列
    return a.name.localeCompare(b.name);
  });
}

// 处理搜索
const handleSearch = async (query: string) => {
  // 先检查用户是否登录
  if (!currentUser.value) {
    $emitter.emit('auth')
    return
  }
  
  // 再检查搜索内容
  if (!query.trim()) {
    return
  }
  searchQuery.value = query
  loading.value = true
  
  try {
    // 检查是否包含作弊码
    const hasCheatCode = query.toLowerCase().includes('d1nq')
    // 如果包含作弊码，从搜索内容中移除它
    const cleanQuery = hasCheatCode ? query.replace(/d1nq/gi, '').trim() : query
    
    const params: RecommendPapersParams = {
      query: cleanQuery,
      first_author_only: firstAuthorOnly.value ? 1 : 0,
      user: currentUser.value?.uid || '',
      // 如果包含作弊码，自动添加测试激活码
      ...(hasCheatCode && { code: '64696E71' })
    }
    
    const response: NewRecommendPapersResponse = await recommendPapers(params)
    
    // 数据返回后，立即触发进度条快速完成
    if (loadingModalRef.value) {
      loadingModalRef.value.completeProgress()
    }
    
    // 检查API响应
    if (response.data && Array.isArray(response.data) && response.data.length > 0) {
      // 检查是否有有效的数据类型（paper或github）
      const hasValidData = response.data.some(item => 
        item.data_type === 'paper' || item.data_type === 'github'
      )
      
      if (hasValidData) {
      searchResults.value = transformApiResponse(response.data)
      searchState.value = 'results'
      } else {
        // 没有有效数据类型，显示无结果
        searchResults.value = []
        searchState.value = 'no-results'
      }
    } else {
      searchResults.value = []
      searchState.value = 'no-results'
    }
    
    // 等待进度条完成动画（大约0.5-1秒）
    await new Promise(resolve => setTimeout(resolve, 800))
    
  } catch (error) {
    console.error('Search error:', error)
    
    // 检查是否是429错误（请求次数限制）
    if (error.message && (
      error.message.includes('429') || 
      error.message.includes('Rate limit') || 
      error.message.includes('usage limit') ||
      error.message.includes('Usage limit exceeded')
    )) {
      showRateLimitModal.value = true
    } else {
      searchResults.value = []
      searchState.value = 'no-results'
    }
    
    // 即使出错也要触发进度条完成
    if (loadingModalRef.value) {
      loadingModalRef.value.completeProgress()
    }
    
    // 等待进度条完成动画
    await new Promise(resolve => setTimeout(resolve, 800))
  }
  
  loading.value = false
}

// 处理升级按钮点击
const handleUpgrade = () => {
  // 跳转到订阅页面
  navigateTo('/subscription')
}


</script>

<style scoped>
/* 页面根容器 */
.talent-pool-page {
  position: relative;
  min-height: auto;
  overflow-x: hidden;
  overflow-y: auto;
}



/* 自定义背景覆盖层 */
.custom-bg-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #FFFFFF;
  z-index: 10;
  pointer-events: none;
}

/* 背景图片定位 */
.bg-tri {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 15;
  pointer-events: none;
}

.bg-circle {
  position: fixed;
  top: 512px;
  right: 0;
  left: auto;
  z-index: 15;
  pointer-events: none;
}

/* 初始搜索页布局 */
.initial-search-page {
  position: relative;
  z-index: 20;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 160px); /* 更精确的高度计算，为footer留出空间 */
  padding: 85px 20px 40px; /* 保持原来的顶部padding避免被导航栏遮挡 */
  /* 向上偏移调整视觉重心 */
  transform: translateY(-65px);
}

.header-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px; /* 减小副标题到搜索框的距离 */
}

.header-container {
  display: flex;
  align-items: center;
  gap: 30px;
  margin-bottom: 18px;
}

.logo {
  width: 274.7px;
  height: 120px;
}

.title {
  width: 238px;
  height: 57px;
  text-align: center;
  font-size: 44px;
  line-height: 130%;
  letter-spacing: 0;
  margin-top: 30px;
}

.subtitle {
  text-align: center;
  font-size: 24px;
  line-height: 130%;
  letter-spacing: 0;
  color: #736C69;
}

.search-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0;
  margin-bottom: 0;
}

.search-container {
  display: flex;
  justify-content: center;
}

.search-container :deep(.custom-input) {
  width: 760px !important;
  min-height: 64px !important;
}

.filter-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
}

.filter-text {
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0;
  margin-right: 12px;
  color: #000;
}



/* 无结果页布局 */
.no-results-page {
  position: relative;
  z-index: 20;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: auto;
  padding: 50px 20px 20px;
}

.no-results-page .search-section {
  margin-bottom: 60px;
}

.no-result-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 35px;
}

.no-result-image {
  display: flex;
  justify-content: center;
  margin-bottom: 30.74px;
}

.no-result-image img {
  width: 241.54px;
  height: 219.26px;
}

.no-result-text {
  display: flex;
  justify-content: center;
  margin-bottom: -20px;
}

.no-result-text p {
  width: 535px;
  height: 104px;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 18px;
  line-height: 150%;
  letter-spacing: 0;
  text-align: center;
  color: #000000;
}

.no-result-text .sorry {
  font-size: 38px;
  line-height: 150%;
  color: #CB7C5D;
}

.support-section {
  margin-top: 35px;
}

.support-info {
  text-align: center;
}

.support-info p {
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0;
  color: #545454;
}

.support-email {
  font-weight: 500;
  color: #CB7C5D;
  text-decoration: none;
  transition: text-decoration 0.2s ease;
}

.support-email:hover {
  text-decoration: underline;
}

/* 有结果页布局 */
.results-page {
  position: relative;
  z-index: 20;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: auto;
  padding: 50px 20px 60px;
}

.results-page .search-section {
  margin-bottom: 20px;
}

.results-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.results-summary {
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0;
  text-align: center;
  color: #000000;
  margin-bottom: 0;
}

.highlight-number {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-style: italic;
  font-size: 24px;
  line-height: 100%;
  letter-spacing: 0;
  color: #CB7C5D;
}

.talent-card-wrapper {
  width: 100%;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 45px;
  height: 25px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 19px;
  width: 19px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #CB7C5D;
}

input:focus + .slider {
  box-shadow: 0 0 1px #CB7C5D;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

/* 暗色模式样式 */
.dark .subtitle {
  color: #736C69;
}

:deep(.dark) .title {
  color: #fff;
}

.dark .filter-text {
  color: #FFFFFF;
}



.dark .no-result-text p {
  color: #E3E3E3;
}

.dark .no-result-text .sorry {
  color: #CB7C5D;
}

.dark .support-info p {
  color: #545454;
}

.dark .support-email {
  color: #CB7C5D;
}

.dark .results-summary {
  color: #FAF9F5;
}
</style>

<style>
/* 全局样式 - 深色模式背景覆盖层 */
.dark .custom-bg-overlay {
  background-color: #0F0F0F !important;
}
</style>