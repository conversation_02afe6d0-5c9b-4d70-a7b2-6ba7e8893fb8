import {ref} from 'vue'
/**
 *  全局 主题数据值
 *  @class light | dark
 */ 
export const GLOBAL_CURRENT_THEME =ref('light');

export const formatThousand = (value: number | string): string => {
  if (value === null || value === undefined || value === '') return '0'

  const num = Number(value)
  if (isNaN(num)) return String(value)

  return num.toLocaleString('en-US')
}

export const convertToLabelValueArray = <T extends Record<string, any>>(
  obj: T,
  keys: Array<keyof T>
): Array<{ label: string; value: T[keyof T] }> => {
  return keys.map((key) => {
    const labelText = String(key)
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase())
      .trim()

    // 处理空值，提供默认值0
    let value = obj[key]
    if (value === null || value === undefined || value === '' || value === 'N/A') {
      value = 0
    }

    return {
      label: labelText,
      value: value,
    }
  })
}

/**
 * 从GitHub链接或用户名中提取用户名
 * 支持的格式：
 * - username
 * - github.com/username
 * - www.github.com/username
 * - https://github.com/username
 * - https://www.github.com/username
 */
export const extractGitHubUsername = (input: string): string => {
  if (!input || typeof input !== 'string') return ''

  const trimmedInput = input.trim()

  // 如果输入不包含github.com，直接返回（假设是用户名）
  if (!trimmedInput.toLowerCase().includes('github.com')) {
    // 去除末尾的斜杠
    return trimmedInput.replace(/\/+$/, '')
  }

  // 匹配GitHub URL中的用户名
  // 支持: github.com/username, www.github.com/username, https://github.com/username 等
  const githubUrlPattern = /(?:https?:\/\/)?(?:www\.)?github\.com\/([^\/\?\s]+)/i
  const match = trimmedInput.match(githubUrlPattern)

  if (match && match[1]) {
    // 去除末尾的斜杠
    return match[1].replace(/\/+$/, '')
  }

  // 如果没有匹配到，返回原始输入（去除末尾斜杠）
  return trimmedInput.replace(/\/+$/, '')
}

// S3上传相关接口
interface PresignedUrlResponse {
  uploadUrl: string
  publicUrl: string
}

// 获取预签名URL
export async function getPresignedUrl(fileType: 'image/png' | 'text/html', fileName?: string): Promise<PresignedUrlResponse> {
  const workerUrl = 'https://get-s3-url.pjharvey071.workers.dev'

  const requestBody: any = { fileType }
  if (fileName) {
    requestBody.fileName = fileName
  }

  const response = await fetch(workerUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestBody),
  })

  const data = await response.json()
  return data // { uploadUrl: string, publicUrl: string }
}

// 直接上传到S3
export async function uploadFileToS3(blob: Blob, fileType: 'image/png' | 'text/html', fileName?: string): Promise<string> {
  // 获取预签名URL
  const { uploadUrl, publicUrl } = await getPresignedUrl(fileType, fileName)

  // 直接上传到S3
  const uploadResponse = await fetch(uploadUrl, {
    method: 'PUT',
    headers: {
      'Content-Type': fileType,
    },
    body: blob,
  })

  if (!uploadResponse.ok) {
    throw new Error(`Failed to upload file to S3: ${uploadResponse.status}`)
  }

  return publicUrl
}

// 生成可预测的OG图片URL（不需要实际上传）
export function getPredictableOgImageUrl(username: string): string {
  const fileName = `github-${username}-latest.png`
  return `https://dinq-share-og.s3.us-east-2.amazonaws.com/shares/${fileName}`
}

// 检查OG图片是否存在
export async function checkOgImageExists(username: string): Promise<boolean> {
  try {
    const url = getPredictableOgImageUrl(username)
    const response = await fetch(url, { method: 'HEAD' })
    return response.ok
  } catch (error) {
    return false
  }
}

// 生成可预测的比较页面OG图片URL
export function getPredictableCompareOgImageUrl(user1: string, user2: string): string {
  const fileName = `github-compare-${user1}-vs-${user2}-latest.png`
  return `https://dinq-share-og.s3.us-east-2.amazonaws.com/shares/${fileName}`
}

// 检查比较页面OG图片是否存在
export async function checkCompareOgImageExists(user1: string, user2: string): Promise<boolean> {
  try {
    const url = getPredictableCompareOgImageUrl(user1, user2)
    const response = await fetch(url, { method: 'HEAD' })
    return response.ok
  } catch (error) {
    return false
  }
}

// Scholar 相关的 OG 图片工具函数

// 生成可预测的 Scholar OG 图片 URL
export function getPredictableScholarOgImageUrl(scholarId: string): string {
  // 清理 scholarId，确保文件名安全
  const cleanId = scholarId.replace(/[^a-zA-Z0-9-_]/g, '-')
  const fileName = `scholar-${cleanId}-latest.png`
  return `https://dinq-share-og.s3.us-east-2.amazonaws.com/shares/${fileName}`
}

// 检查 Scholar OG 图片是否存在
export async function checkScholarOgImageExists(scholarId: string): Promise<boolean> {
  try {
    const url = getPredictableScholarOgImageUrl(scholarId)
    const response = await fetch(url, { method: 'HEAD' })
    return response.ok
  } catch (error) {
    return false
  }
}

// 从查询字符串中提取可能的 Scholar ID
export function extractScholarId(query: string): string | null {
  if (!query || typeof query !== 'string') return null

  const trimmedQuery = query.trim()

  // 检查是否是 Google Scholar URL
  const scholarUrlPattern = /(?:scholar\.google\.com\/citations\?user=|user=)([a-zA-Z0-9_-]+)/i
  const match = trimmedQuery.match(scholarUrlPattern)

  if (match && match[1]) {
    return match[1]
  }

  // 如果看起来像 Scholar ID（字母数字组合，通常10-12位）
  if (/^[a-zA-Z0-9_-]{8,15}$/.test(trimmedQuery)) {
    return trimmedQuery
  }

  return null
}
