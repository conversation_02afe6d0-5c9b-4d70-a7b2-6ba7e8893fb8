/**
 * 邮箱格式验证工具函数
 */

/**
 * 验证邮箱格式是否有效
 * @param email 邮箱地址
 * @returns 是否为有效邮箱格式
 */
export const isValidEmail = (email: string): boolean => {
  if (!email || typeof email !== 'string') {
    return false
  }
  
  // 基本的邮箱格式验证正则表达式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email.trim())
}

/**
 * 更严格的邮箱格式验证
 * @param email 邮箱地址
 * @returns 是否为有效邮箱格式
 */
export const isValidEmailStrict = (email: string): boolean => {
  if (!email || typeof email !== 'string') {
    return false
  }
  
  // 更严格的邮箱验证正则表达式
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
  return emailRegex.test(email.trim())
}

/**
 * 验证密码强度
 * @param password 密码
 * @returns 密码强度信息
 */
export const validatePasswordStrength = (password: string): {
  isValid: boolean
  score: number
  feedback: string[]
} => {
  const feedback: string[] = []
  let score = 0
  
  if (!password) {
    return {
      isValid: false,
      score: 0,
      feedback: ['Password is required']
    }
  }
  
  // 长度检查
  if (password.length < 6) {
    feedback.push('Password should be at least 6 characters long')
  } else if (password.length >= 8) {
    score += 1
  }
  
  // 包含小写字母
  if (/[a-z]/.test(password)) {
    score += 1
  } else {
    feedback.push('Password should contain lowercase letters')
  }
  
  // 包含大写字母
  if (/[A-Z]/.test(password)) {
    score += 1
  }
  
  // 包含数字
  if (/\d/.test(password)) {
    score += 1
  }
  
  // 包含特殊字符
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    score += 1
  }
  
  const isValid = password.length >= 6 && feedback.length === 0
  
  return {
    isValid,
    score,
    feedback
  }
}

/**
 * 验证用户名格式
 * @param username 用户名
 * @returns 是否为有效用户名
 */
export const isValidUsername = (username: string): boolean => {
  if (!username || typeof username !== 'string') {
    return false
  }
  
  // 用户名应该是3-20个字符，只包含字母、数字、下划线和连字符
  const usernameRegex = /^[a-zA-Z0-9_-]{3,20}$/
  return usernameRegex.test(username.trim())
}

/**
 * 验证显示名称格式
 * @param displayName 显示名称
 * @returns 是否为有效显示名称
 */
export const isValidDisplayName = (displayName: string): boolean => {
  if (!displayName || typeof displayName !== 'string') {
    return false
  }
  
  const trimmed = displayName.trim()
  
  // 显示名称应该是1-50个字符，不能只包含空格
  return trimmed.length >= 1 && trimmed.length <= 50
}
