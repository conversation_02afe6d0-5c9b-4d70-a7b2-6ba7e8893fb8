# Firebase 邮箱密码认证设置指南

## 🔧 Firebase 控制台配置

### 1. 启用邮箱密码认证

1. 打开 [Firebase 控制台](https://console.firebase.google.com)
2. 选择你的项目 `dinq-1a6c0`
3. 在左侧菜单中点击 **Authentication**
4. 点击 **Sign-in method** 标签页
5. 在 **Sign-in providers** 列表中找到 **Email/Password**
6. 点击 **Email/Password** 进行编辑
7. 启用 **Email/Password** 选项
8. 可选：启用 **Email link (passwordless sign-in)** 如果你想要无密码登录
9. 点击 **Save** 保存设置

### 2. 配置邮箱验证（可选但推荐）

1. 在 Authentication 页面，点击 **Templates** 标签页
2. 配置邮箱验证模板
3. 设置发件人邮箱地址

### 3. 设置密码策略（可选）

1. 在 Authentication 页面，点击 **Settings** 标签页
2. 找到 **Password policy** 部分
3. 配置密码复杂度要求：
   - 最小长度
   - 必须包含大写字母
   - 必须包含小写字母
   - 必须包含数字
   - 必须包含特殊字符

## 🚀 前端实现完成

### ✅ 已实现的功能

1. **邮箱密码登录** - `loginWithEmail(email, password)`
2. **邮箱密码注册** - `registerWithEmail(email, password)`
3. **密码重置** - `resetPassword(email)`
4. **邮箱验证完整流程**：
   - `sendVerificationEmail()` - 发送验证邮件
   - `refreshEmailVerificationStatus()` - 刷新验证状态
   - **智能验证提醒** - 使用 `EmailVerificationModal` 组件
   - **Firebase 托管验证** - 验证链接由 Firebase 处理 (auth.dinq.io)
5. **Google OAuth 登录** - `loginWithGoogle()` (原有功能)
6. **登出** - `logout()`

### 🎯 **新增：智能邮箱验证提醒系统**

#### **EmailVerificationModal 组件特性**：
- ✅ **智能提醒时机** - 登录后3秒自动检查并提醒
- ✅ **防重复提醒** - "稍后提醒"功能，24小时内不再提醒
- ✅ **新用户保护** - 注册后5分钟内不重复提醒
- ✅ **一键重发** - 60秒倒计时防止频繁发送
- ✅ **状态检查** - "我已验证邮箱"按钮实时检查状态
- ✅ **用户友好** - 清晰的UI和帮助信息

### 📧 **完整的邮箱验证流程**

#### 1. **用户注册** → 自动发送验证邮件
```typescript
// 注册时自动发送验证邮件
await registerWithEmail('<EMAIL>', 'password123')
// ✅ 账户创建成功
// ✅ 自动发送验证邮件到用户邮箱
```

#### 2. **用户收到邮件** → 点击验证链接
- 用户在邮箱中收到来自 Firebase 的验证邮件
- 邮件包含验证链接，格式类似：
  ```
  https://your-domain.com/verify-email?mode=verifyEmail&oobCode=ABC123...
  ```

#### 3. **点击链接** → 自动验证
- 用户点击邮件中的验证链接
- 浏览器打开 `/verify-email` 页面
- 页面自动处理验证码完成验证
- 显示验证成功或失败的结果

#### 4. **验证完成** → 账户激活
```typescript
// 验证成功后，用户的 emailVerified 状态变为 true
const user = auth.currentUser;
console.log(user.emailVerified); // true
```

### 🎯 完全符合 Firebase 官方文档

我们的实现完全遵循了 Firebase 官方文档的最佳实践：

#### ✅ 创建基于密码的账号
```typescript
// 完全按照官方文档实现
const userCredential = await createUserWithEmailAndPassword(auth, email, password);
// Signed up - 创建新账号后，用户会自动登录
const user = userCredential.user;
```

#### ✅ 邮箱密码登录
```typescript
// 完全按照官方文档实现
const userCredential = await signInWithEmailAndPassword(auth, email, password);
// Signed in
const user = userCredential.user;
```

#### ✅ 详细错误处理
```typescript
// 按照官方建议同时捕获 errorCode 和 errorMessage
catch (err: any) {
  const errorCode = err.code;
  const errorMessage = err.message;
  // 提供用户友好的错误信息
}
```

### 🎨 UI 组件更新

1. **AuthModal 组件**：
   - 添加了邮箱密码表单
   - 支持登录/注册模式切换
   - 集成忘记密码功能
   - 保留原有的 Google 登录
   - 添加了 "Continue with Email" 按钮

2. **useFirebaseAuth Composable**：
   - 扩展了认证方法
   - 添加了错误处理
   - 保持向后兼容

### 🧪 测试页面

#### 1. **主要测试页面** - `/test-email-auth`
- 邮箱密码登录
- 邮箱密码注册（自动发送验证邮件）
- 密码重置
- 邮箱验证状态检查
- 手动发送邮箱验证邮件
- 刷新验证状态
- Google 登录
- 登出

#### 2. **邮箱验证页面** - `/verify-email`
- 处理邮件中的验证链接
- 显示验证结果（成功/失败）
- 重新发送验证邮件
- 刷新验证状态

### 🔄 **完整的用户体验流程**

1. **用户注册**：
   - 填写邮箱和密码
   - 点击"Sign Up"
   - 看到提示："Account created successfully! A verification email has been sent..."

2. **检查邮箱**：
   - 用户去邮箱查看验证邮件
   - 邮件标题类似："Verify your email for [Your App]"

3. **点击验证链接**：
   - 用户点击邮件中的"Verify Email"按钮
   - 浏览器自动打开 `/verify-email` 页面
   - 页面显示验证进度和结果

4. **验证完成**：
   - 看到"Email Verified Successfully!"消息
   - 点击"Continue to App"返回应用
   - 用户账户现在完全激活

### 🔍 改进的错误处理

我们实现了比官方示例更详细的错误处理：

```typescript
const getFirebaseErrorMessage = (errorCode: string, errorMessage: string): string => {
  switch (errorCode) {
    case 'auth/user-not-found':
      return 'No account found with this email address. Please check your email or sign up for a new account.';
    case 'auth/wrong-password':
      return 'Incorrect password. Please try again or reset your password.';
    case 'auth/email-already-in-use':
      return 'An account with this email address already exists. Please sign in instead.';
    case 'auth/weak-password':
      return 'Password should be at least 6 characters long.';
    case 'auth/invalid-email':
      return 'Please enter a valid email address.';
    case 'auth/too-many-requests':
      return 'Too many failed attempts. Please try again later or reset your password.';
    // ... 更多错误类型
    default:
      return errorMessage || 'An unexpected error occurred. Please try again.';
  }
};
```

## 📝 使用示例

### 在组件中使用

```vue
<script setup>
const { 
  currentUser, 
  loading, 
  error, 
  loginWithEmail, 
  registerWithEmail, 
  resetPassword,
  loginWithGoogle,
  logout 
} = useFirebaseAuth()

// 邮箱登录
const handleEmailLogin = async () => {
  try {
    await loginWithEmail('<EMAIL>', 'password123')
    console.log('登录成功!')
  } catch (error) {
    console.error('登录失败:', error)
  }
}

// 邮箱注册
const handleEmailRegister = async () => {
  try {
    await registerWithEmail('<EMAIL>', 'password123')
    console.log('注册成功!')
  } catch (error) {
    console.error('注册失败:', error)
  }
}

// 密码重置
const handlePasswordReset = async () => {
  try {
    await resetPassword('<EMAIL>')
    console.log('密码重置邮件已发送!')
  } catch (error) {
    console.error('密码重置失败:', error)
  }
}
</script>
```

### AuthModal 使用

```vue
<template>
  <AuthModal
    :visible="showModal"
    :loading="loading"
    @update:visible="showModal = $event"
    @auth="handleOAuthAuth"
  />
</template>

<script setup>
const handleOAuthAuth = async (provider) => {
  // 处理 Google/Twitter 登录
  if (provider === 'google') {
    await loginWithGoogle()
  }
}
</script>
```

## 🔒 安全注意事项

1. **密码策略**：建议在 Firebase 控制台设置强密码策略
2. **邮箱验证**：建议启用邮箱验证以确保用户邮箱有效
3. **错误处理**：已实现友好的错误信息显示
4. **防暴力破解**：Firebase 自动提供防暴力破解保护

## 🎯 下一步

1. 在 Firebase 控制台启用邮箱密码认证
2. 测试 `/test-email-auth` 页面
3. 根据需要调整 UI 样式
4. 配置邮箱验证模板
5. 设置密码策略

## 📞 支持

如果遇到问题，请检查：
1. Firebase 控制台是否正确配置
2. 浏览器控制台的错误信息
3. 网络连接是否正常
